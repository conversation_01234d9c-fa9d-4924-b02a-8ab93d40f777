# 🔧 Debug Guide: View 3D Model không hiển thị

## Vấn đề đã được sửa
✅ **Lỗi Unicode encoding** - <PERSON><PERSON> thay emoji bằng text thường  
✅ **Window positioning** - Cửa sổ được center và force to foreground  
✅ **Process launch** - Sử dụng DETACHED_PROCESS trên Windows  

## Các bước debug

### 1. Kiểm tra server đang chạy
```bash
# Terminal 1: Start server
python run.py

# Kiểm tra server response
curl http://localhost:8124/
```

### 2. Test GUI trực tiếp
```bash
# Test GUI script trực tiếp
python Tolery/gui_step.py "outputs/code/cad_outputs_generated/Rectangular_box.step"

# Nếu thành công, bạn sẽ thấy:
# [INFO] Window positioned at (560, 240)
# [SUCCESS] Window brought to foreground (Windows)
# [SUCCESS] 3D Model Viewer opened successfully
```

### 3. Test API endpoint
```bash
# Terminal 2: Test API
python test_api_endpoint.py

# Hoặc test bằng curl
curl -X POST http://localhost:8124/api/launch-step-viewer
```

### 4. Test từ web interface
1. Mở browser: `http://localhost:8124`
2. Tạo mô hình: nhập "rectangular 20x30x40"
3. Nhấn Generate
4. Khi code xuất hiện, nhấn nút **"View 3D Model"**

## Các vấn đề có thể gặp

### A. Cửa sổ không hiển thị
**Nguyên nhân có thể:**
- Cửa sổ bị ẩn đằng sau cửa sổ khác
- Cửa sổ mở ở vị trí off-screen
- Display driver issues

**Giải pháp:**
1. **Kiểm tra taskbar** - tìm icon "3D Model Viewer"
2. **Alt+Tab** để cycle qua các cửa sổ
3. **Windows key + Tab** để xem tất cả cửa sổ
4. **Nhấn nút View 3D Model nhiều lần** - mỗi lần mở cửa sổ mới

### B. API trả về lỗi
**Kiểm tra log server** để xem lỗi chi tiết:
```
[ERROR] 3D Model Viewer script not found
[ERROR] No STEP files found
[ERROR] Failed to launch 3D Model Viewer process
```

### C. Process launch thành công nhưng không thấy cửa sổ
**Debug steps:**
```bash
# 1. Kiểm tra process đang chạy
python debug_gui_issue.py

# 2. Kill tất cả GUI processes
taskkill /f /im python.exe

# 3. Test lại
python test_api_endpoint.py
```

## Logs để kiểm tra

### Server logs (khi nhấn View 3D Model):
```
[SUCCESS] 3D Model Viewer launched successfully with PID: XXXX
[DEBUG] Command executed: python gui_step.py file.step
[DEBUG] Working directory: D:\DFM_ATN_TOLERY\tolery-api-ai
[SUCCESS] 3D Model Viewer should now be visible
```

### GUI logs:
```
Loading STEP file: outputs/...
Successfully loaded ... Found 6 faces and 24 edges
[INFO] Window positioned at (560, 240)
[SUCCESS] Window brought to foreground (Windows)
[SUCCESS] 3D Model Viewer opened successfully
```

## Test scripts có sẵn

1. **`test_gui_launch.py`** - Test launch logic
2. **`test_api_endpoint.py`** - Test API endpoint
3. **`debug_gui_issue.py`** - Debug GUI issues
4. **`test_simple_gui.py`** - Test basic PyQt5

## Nếu vẫn không hoạt động

### Kiểm tra dependencies:
```bash
pip list | grep -i pyqt
pip list | grep -i occ
```

### Reinstall nếu cần:
```bash
conda install -c conda-forge pythonocc-core
pip install PyQt5
```

### Kiểm tra display:
- Đảm bảo không chạy qua Remote Desktop
- Kiểm tra multiple monitors setup
- Thử disconnect external monitors

## Workflow hoạt động đúng

1. ✅ User tạo mô hình → FreeCAD code generated → STEP file created
2. ✅ Nút "View 3D Model" xuất hiện
3. ✅ User nhấn nút → JavaScript gọi `/api/launch-step-viewer`
4. ✅ Backend tìm STEP file mới nhất → Launch gui_step.py
5. ✅ GUI window xuất hiện ở center screen, on top
6. ✅ User có thể tương tác với 3D model
7. ✅ User đóng window → Có thể nhấn nút lại để mở window mới

---

**Nếu tất cả test scripts đều báo SUCCESS nhưng vẫn không thấy cửa sổ:**
- Cửa sổ có thể đang hiển thị nhưng bị ẩn
- Kiểm tra kỹ taskbar và Alt+Tab
- Thử nhấn nút View 3D Model nhiều lần
- Restart máy tính nếu cần thiết
