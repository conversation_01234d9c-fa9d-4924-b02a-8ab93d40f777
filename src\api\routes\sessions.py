"""
API routes for session-related operations.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Query, HTTPException, Path
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
from sqlalchemy.exc import SQLAlchemyError

# Set up logging
logger = logging.getLogger("tolery-api")

try:
    from ... import crud
    from ...schemas.sessions import SessionInfo
    from ...models.sessions import Session as SessionModel
except ImportError:
    # Fallback for direct imports
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    import src.crud as crud
    from src.schemas.sessions import SessionInfo
    from src.models.sessions import Session as SessionModel

# Create router
router = APIRouter(
    prefix="/sessions",
    tags=["sessions"],
    responses={404: {"description": "Not found"}},
)

# Import the database dependency from a common location
try:
    from ...database.database import get_db
except ImportError:
    # Fallback for direct imports
    from src.database.database import get_db


@router.get("/", summary="List Sessions", response_model=List[SessionInfo])
def list_sessions(db: Session = Depends(get_db)):
    """
    List all available sessions with their details.
    """
    try:
        sessions = crud.get_all_sessions(db)
        # Pydantic will handle the conversion of Session model objects to SessionInfo schema objects
        # because from_attributes = True (or orm_mode = True) is set in the SessionInfo schema.
        return sessions
    except SQLAlchemyError as e:
        logger.error(f"Database error when listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving sessions")
    except Exception as e:
        logger.error(f"Unexpected error when listing sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get(
    "/{session_id}",
    response_model=SessionInfo,
    summary="Get Session Info",
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": {
                        "id": 1,
                        "session_id": "some-uuid-string",
                        "name": "Session Name",
                        "created_at": "2023-10-27T10:00:00.000Z",
                        "updated_at": "2023-10-27T10:30:00.000Z"
                    }
                }
            }
        },
    }
)
def get_session(session_id: str, db: Session = Depends(get_db)):
    """
    Get information about a specific session.
    """
    try:
        session: Optional[SessionModel] = crud.get_session_by_id(db, session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Return the session object
        return session
    except HTTPException:
        # Re-raise HTTP exceptions (like 404)
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when getting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving session")
    except Exception as e:
        logger.error(f"Unexpected error when getting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.delete(
    "/{session_id}",
    summary="Delete Session",
    response_model=str,
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": "string"  # As per image
                }
            }
        }
        # FastAPI will automatically handle 404 from crud.delete_session
        # and 400 (e.g., if deletion_type is invalid from crud.delete_session)
    }
)
def delete_session(
    session_id: str = Path(...),
    deletion_type: str = Query(
        "soft"
    ),
    db: Session = Depends(get_db)
):
    """
    Delete or reset a session's data.
    """
    try:
        response_dict = crud.delete_session(db=db, session_id=session_id, deletion_type=deletion_type)
        # crud.delete_session returns a dict like {"message": "..."} or raises HTTPException
        return response_dict.get("message", "Session processed successfully.")
    except HTTPException:
        # Re-raise HTTP exceptions (like 404 or 400)
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when deleting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while deleting session")
    except Exception as e:
        logger.error(f"Unexpected error when deleting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get(
    "/{session_id}/chat",
    summary="Get Chat History",
    response_model=List[dict],
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": 1,
                            "message": "Create a rectangular box 20x30x40",
                            "response": "I'll create a rectangular box with dimensions 20x30x40 for you.",
                            "created_at": "2023-10-27T10:00:00.000Z"
                        }
                    ]
                }
            }
        }
        # FastAPI will automatically handle 404 if session_id is invalid
        # or 422 if validation for limit fails.
    }
)
def get_chat_history(
    session_id: str = Path(..., description="The session ID to retrieve chat history for"),
    limit: int = Query(50, description="Maximum number of messages to return", ge=1),
    db: Session = Depends(get_db)
):
    """
    Get chat history for a specific session.
    """
    try:
        # page is defaulted to 1 when calling crud.get_chats_by_session_id
        chat_history_objects = crud.get_chats_by_session_id(db, session_id, page=1, limit=limit)

        if not chat_history_objects:
            # Return an empty array if no history found
            return []

        # Convert the list of ChatHistory objects to a list of dicts
        history_data = []
        for entry in chat_history_objects:
            entry_data = {
                'id': entry.id,
                'message': entry.message,
                'response': entry.response,  # Include chat response from chatbot
                'created_at': entry.created_at.isoformat() if entry.created_at else None,
                # 'output' field is intentionally excluded as per requirement
            }
            history_data.append(entry_data)

        return history_data

    except SQLAlchemyError as e:
        logger.error(f"Database error when getting chat history for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving chat history")
    except Exception as e:
        logger.error(f"Error getting chat history for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Could not retrieve chat history")


@router.get(
    "/{session_id}/exports",
    summary="Get Export Links",
    response_model=List[str],
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": [
                        "path/to/export1.obj",
                        "path/to/export2.obj"
                    ]
                }
            }
        }
        # FastAPI will automatically handle 404 if session_id is invalid
        # or 422 if validation for export_format fails.
    }
)
def get_exports(
    session_id: str = Path(..., description="The session ID to retrieve export links for"),
    export_format: Optional[str] = Query(
        default=None,
        description="Filter by export format (obj, step, dxf)"
    ),
    db: Session = Depends(get_db)
):
    """
    Get the obj_export paths for a specific session.

    Returns a list of obj_export paths from the chat history table.
    """
    try:
        # crud.get_exports_by_session_id returns a list of obj_export paths
        obj_export_paths = crud.get_exports_by_session_id(db, session_id, export_format)

        # Return the obj_export paths directly as a list
        return obj_export_paths
    except HTTPException:
        # Re-raise HTTP exceptions (like 404)
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when getting exports for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving exports")
    except Exception as e:
        logger.error(f"Unexpected error when getting exports for session {session_id}: {str(e)}")
        # Return an empty array in case of error
        return []


@router.get(
    "/{session_id}/latest-code",
    summary="Get Latest Code",
    response_model=dict,
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": {
                        "latest_code": "import FreeCAD as App\n# Generated code...",
                        "session_id": "session_abc123_456789"
                    }
                }
            }
        },
        404: {
            "description": "No code found for this session"
        }
    }
)
def get_latest_code(
    session_id: str = Path(..., description="The session ID to retrieve latest code for"),
    db: Session = Depends(get_db)
):
    """
    Get the latest generated code for a specific session.
    """
    try:
        logger.info(f"🔍 [LATEST CODE API] Fetching latest code for session: {session_id}")

        # Use the existing get_latest_code function from crud.sessions
        from src.crud.sessions import get_latest_code
        latest_code = get_latest_code(db, session_id)

        if latest_code:
            logger.info(f"✅ [LATEST CODE API] Found code for session {session_id}, length: {len(latest_code)} characters")
            logger.info(f"🔍 [LATEST CODE API] Code preview: {latest_code[:100]}...")

            return {
                "latest_code": latest_code,
                "session_id": session_id
            }
        else:
            logger.warning(f"❌ [LATEST CODE API] No code found for session {session_id}")
            raise HTTPException(status_code=404, detail="No code found for this session")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when getting latest code for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving latest code")
    except Exception as e:
        logger.error(f"Unexpected error when getting latest code for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")


@router.get(
    "/{session_id}/tessellated",
    summary="Get Tessellated Object",
    response_model=dict,
    responses={
        200: {
            "description": "Successful Response",
            "content": {
                "application/json": {
                    "example": {
                        "vertices": [[0, 0, 0], [1, 0, 0], [0, 1, 0]],
                        "faces": [[0, 1, 2]],
                        "normals": [[0, 0, 1]]
                    }
                }
            }
        }
        # FastAPI will automatically handle 404 if session_id is invalid
        # or 422 if validation fails.
    }
)
def get_tessellation(
    session_id: str = Path(..., description="The session ID to retrieve tessellated object for"),
    db: Session = Depends(get_db)
):
    """
    Get the tessellated object for a specific session.
    """
    try:
        # crud.get_tessellation_by_session_id returns a dict
        tessellation_data = crud.get_tessellation_by_session_id(db, session_id)

        # Return the tessellation data directly
        if tessellation_data is None:
            # Return an empty dict if no data found
            return {}
        return tessellation_data
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when getting tessellation for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while retrieving tessellation data")
    except Exception as e:
        logger.error(f"Unexpected error when getting tessellation for session {session_id}: {str(e)}")
        # Return an empty dict in case of error
        return {}
