#!/usr/bin/env python3
"""
Simple GUI test to check if PyQt5 windows can be displayed.
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

class SimpleTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple GUI Test - Tolery")
        self.setGeometry(100, 100, 400, 300)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Add widgets
        label = QLabel("If you can see this window, PyQt5 is working!")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("Close")
        button.clicked.connect(self.close)
        layout.addWidget(button)
        
        # Force window to be visible and on top
        self.show()
        self.raise_()
        self.activateWindow()
        
        # Windows-specific: bring to foreground
        if os.name == 'nt':
            try:
                import ctypes
                hwnd = int(self.winId())
                ctypes.windll.user32.SetForegroundWindow(hwnd)
                ctypes.windll.user32.BringWindowToTop(hwnd)
                print("[SUCCESS] Window brought to foreground")
            except Exception as e:
                print(f"[WARNING] Could not bring window to foreground: {e}")

def main():
    print("Testing simple PyQt5 window...")
    
    app = QApplication(sys.argv)
    
    window = SimpleTestWindow()
    print("[INFO] Simple test window created and shown")
    print("[INFO] If you don't see the window, there's a display issue")
    
    # Run for 10 seconds then exit
    import time
    start_time = time.time()
    while time.time() - start_time < 10:
        app.processEvents()
        time.sleep(0.1)
    
    print("[INFO] Test completed")
    window.close()
    app.quit()

if __name__ == "__main__":
    main()
