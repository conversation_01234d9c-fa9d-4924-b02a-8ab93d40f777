@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.typing-indicator {
  display: flex;
  padding: 8px 12px;
}
.typing-dot {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: #6b7280;
  border-radius: 50%;
  animation: pulse 1.5s infinite ease-in-out;
}
.typing-dot:nth-child(1) {
  animation-delay: 0s;
}
.typing-dot:nth-child(2) {
  animation-delay: 0.3s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0.6s;
}

#chatbot-widget {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 400px;
  height: 500px;
  z-index: 1000;
  transition: all 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
  visibility: hidden;
}

#chatbot-widget.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

#chat-toggle {
  position: fixed;
  bottom: 20px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #4f46e5;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  transition: opacity 0.3s ease;
}

#chat-toggle.hidden {
  opacity: 0;
  pointer-events: none;
}

.chat-messages {
  height: calc(100% - 110px);
}

.message {
  opacity: 0;
  transform: translateY(5px);
  animation: fadeIn 0.2s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#edit-mode-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(79, 70, 229, 0.1);
  transition: all 0.3s ease;
}

#edit-mode-indicator.hidden {
  display: none;
}

/* Chat history styles */
#chat-history {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

#chat-history::-webkit-scrollbar {
  width: 8px;
}

#chat-history::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

#chat-history::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 4px;
  border: 2px solid #f7fafc;
}

.use-code-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.use-code-btn:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

/* Fade-out animation for notifications */
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  } /* Stay visible for 80% of the animation time */
  100% {
    opacity: 0;
  }
}

.fade-out {
  animation: fadeOut 3s forwards;
}

/* Bot message styling - updated with greater specificity */
html body #code-output div.message.bot {
  background-color: #e8f4ff !important;
  border-left: 3px solid #4299e1 !important;
  color: #2c5282 !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  max-width: 90% !important;
  margin-bottom: 10px !important;
}

/* Additional style for bot messages regardless of container */
div.message.bot {
  background-color: #e8f4ff !important;
  border-left: 3px solid #4299e1 !important;
  color: #2c5282 !important;
}

/* Enhanced Loading Animations and UX */

/* Global loading overlay */
#global-loading-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(4px);
  z-index: 9999;
}

/* Main loading spinner */
.loading-spinner {
  width: 64px;
  height: 64px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: relative;
}

.loading-spinner::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #8b5cf6;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: spin-reverse 0.8s linear infinite;
}

/* Small loading spinner */
.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  display: inline-block;
}

/* Loading dots animation */
.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4f46e5;
  animation: loading-pulse 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

/* Enhanced typing indicator */
.enhanced-typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.enhanced-typing-indicator .typing-dot {
  width: 6px;
  height: 6px;
  background-color: #6366f1;
  border-radius: 50%;
  animation: typing-bounce 1.4s infinite ease-in-out both;
}

.enhanced-typing-indicator .typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.enhanced-typing-indicator .typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.enhanced-typing-indicator .typing-dot:nth-child(3) {
  animation-delay: 0s;
}

/* Robot icon animation */
.typing-robot-icon {
  animation: robot-pulse 2s infinite ease-in-out;
}

/* Form processing state */
.processing {
  pointer-events: none;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.processing textarea {
  background-color: #f9fafb !important;
  cursor: not-allowed;
}

.processing button {
  cursor: not-allowed;
}

/* Fade animations */
.loading-fade-in {
  animation: fade-in 0.3s ease-out;
}

.loading-fade-out {
  animation: fade-out 0.3s ease-in forwards;
}

/* Button loading states */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  transform: none !important;
}

button.loading {
  position: relative;
  color: transparent !important;
}

button.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 0.8s linear infinite;
}

/* Enhanced pulse animation for loading states */
.pulse-loading {
  animation: pulse-loading 2s infinite;
}

/* Progress bar animation */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #4f46e5, transparent);
  animation: progress-slide 2s infinite;
}

/* Shimmer effect for loading content */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
}

.skeleton-text:last-child {
  width: 60%;
}

/* Enhanced hover effects during non-loading states */
button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

button:not(:disabled):active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Loading overlay for code output */
.code-output-loading {
  position: relative;
}

.code-output-loading::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 10;
  border-radius: 8px;
}

/* Success animation */
.success-pulse {
  animation: success-pulse 0.6s ease-out;
}

/* Error shake animation */
.error-shake {
  animation: error-shake 0.5s ease-in-out;
}

/* Keyframe animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

@keyframes loading-pulse {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes robot-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes pulse-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes progress-slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes success-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes error-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

/* Responsive loading improvements */
@media (max-width: 640px) {
  #global-loading-overlay .bg-white {
    margin: 16px;
    padding: 24px;
  }

  .loading-spinner {
    width: 48px;
    height: 48px;
  }

  .loading-spinner::before {
    width: 24px;
    height: 24px;
  }
}

/* Dark mode loading states */
@media (prefers-color-scheme: dark) {
  .loading-spinner {
    border-color: #374151;
    border-top-color: #6366f1;
  }

  .loading-spinner::before {
    border-color: #4b5563;
    border-top-color: #8b5cf6;
  }

  .skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

/* Accessibility improvements for loading states */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner,
  .loading-spinner::before,
  .loading-spinner-small,
  .loading-dot,
  .typing-robot-icon {
    animation: none;
  }

  .loading-fade-in,
  .loading-fade-out {
    animation: none;
    opacity: 1;
  }

  .processing {
    transition: none;
  }
}

/* Focus states for loading buttons */
button:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

button:disabled:focus-visible {
  outline: 2px solid #9ca3af;
}

/* Loading state for file uploads */
.file-upload-loading {
  position: relative;
  overflow: hidden;
}

.file-upload-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #4f46e5, transparent);
  animation: progress-slide 1.5s infinite;
}

/* Enhanced Notification System */
.notification-enter {
  animation: notification-slide-in 0.4s ease-out;
}

.notification-exit {
  animation: notification-slide-out 0.3s ease-in forwards;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #4f46e5, #8b5cf6);
  animation: notification-progress 4s linear forwards;
  border-radius: 0 0 8px 8px;
}

@keyframes notification-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes notification-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes notification-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Character count animation */
#char-count {
  transition: transform 0.15s ease, color 0.2s ease;
}

#char-count.warning {
  color: #f59e0b;
}

#char-count.danger {
  color: #ef4444;
}

/* Enhanced button transitions */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:not(:disabled):active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Loading state improvements */
.processing #user-input {
  background-color: #f9fafb;
  cursor: not-allowed;
  transition: background-color 0.3s ease;
}

.processing .bg-white {
  background-color: #f8fafc;
  transition: background-color 0.3s ease;
}

/* Enhanced typing indicator */
.typing-indicator-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 12px;
  border: 1px solid #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* File upload enhancements */
.file-upload-area.processing {
  background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
  background-size: 20px 20px;
  animation: diagonal-lines 1s linear infinite;
}

@keyframes diagonal-lines {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}

/* Progress indicator for long operations */
.operation-progress {
  position: relative;
  overflow: hidden;
  background-color: #f3f4f6;
  border-radius: 6px;
  height: 6px;
  margin: 8px 0;
}

.operation-progress::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #8b5cf6, #06b6d4);
  background-size: 200% 100%;
  animation: progress-gradient 2s ease-in-out infinite;
  border-radius: 6px;
  width: var(--progress, 0%);
  transition: width 0.3s ease;
}

@keyframes progress-gradient {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced focus states */
input:focus,
textarea:focus,
button:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
  transition: outline 0.2s ease;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-indicator.success {
  background-color: #dcfce7;
  color: #166534;
}

.status-indicator.error {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Mobile responsiveness for loading states */
@media (max-width: 640px) {
  .notification-enter,
  .notification-exit {
    top: 8px;
    right: 8px;
    left: 8px;
    max-width: none;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }

  .loading-spinner::before {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }

  #global-loading-overlay .bg-white {
    margin: 12px;
    padding: 20px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .notification-enter,
  .notification-exit {
    animation: none;
  }

  .notification-progress {
    animation: none;
  }

  button:hover {
    transform: none;
  }

  .loading-spinner,
  .loading-spinner::before,
  .loading-spinner-small {
    animation: none;
  }

  .operation-progress::before {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .notification-enter {
    border: 2px solid;
  }

  .loading-spinner {
    border-width: 3px;
  }

  .status-indicator {
    border: 1px solid;
  }
}

/* Print styles */
@media print {
  .notification-enter,
  .notification-exit,
  #global-loading-overlay,
  .loading-spinner,
  .loading-spinner-small {
    display: none !important;
  }
}
