# 3D Model Viewer - Hướng dẫn sử dụng

## Tổng quan
Nút **"View 3D Model"** cho phép bạn xem mô hình 3D vừa được tạo ra trong một cửa sổ viewer tương tác. <PERSON><PERSON><PERSON> là một tính năng quan trọng giúp bạn kiểm tra và xác nhận mô hình CAD đã được tạo đúng theo yêu cầu.

## Cách sử dụng

### 1. Tạo mô hình CAD
- Nhập mô tả mô hình vào ô text (ví dụ: "rectangular 20x30x40")
- Nhấn **Generate** hoặc **Enter**
- Ch<PERSON> hệ thống tạo FreeCAD code và file STEP

### 2. Mở 3D Model Viewer
- Sau khi code được tạo thành công, nút **"View 3D Model"** sẽ xuất hiện
- Nhấn vào nút này để mở cửa sổ 3D viewer
- Cửa sổ viewer sẽ hiển thị mô hình 3D với các tính năng tương tác

### 3. Tương tác với mô hình 3D
- **Xoay mô hình**: Kéo chuột trái
- **Pan (di chuyển)**: Kéo chuột giữa hoặc Shift + chuột trái
- **Zoom**: Cuộn chuột hoặc chuột phải
- **Chọn face/edge**: Click chuột trái vào bề mặt hoặc cạnh
- **Xem thông tin**: Thông tin về face/edge được chọn sẽ hiển thị trong popup

### 4. Mở lại viewer khi cần
- Nút **"View 3D Model"** có thể được nhấn nhiều lần
- Mỗi lần nhấn sẽ mở một cửa sổ viewer mới với mô hình mới nhất
- Bạn có thể mở lại viewer bất cứ lúc nào sau khi đã tạo mô hình

## Tính năng của 3D Viewer

### Hiển thị thông tin chi tiết
- **Face information**: Diện tích, tâm khối lượng
- **Edge information**: Chiều dài, điểm đầu/cuối
- **Model statistics**: Số lượng face và edge

### Giao diện thân thiện
- Tiêu đề cửa sổ hiển thị tên mô hình
- Thông báo loading khi đang mở
- Thông báo lỗi rõ ràng nếu có vấn đề

### Tương thích đa định dạng
- Hỗ trợ file STEP (định dạng chính)
- Tự động tìm file mô hình mới nhất
- Kiểm tra tính hợp lệ của file trước khi mở

## Xử lý lỗi

### Nếu nút không xuất hiện
- Đảm bảo đã tạo mô hình thành công (có FreeCAD code)
- Kiểm tra console để xem có lỗi không
- Thử refresh trang và tạo lại mô hình

### Nếu viewer không mở
- Kiểm tra file STEP đã được tạo trong thư mục outputs/
- Đảm bảo PyQt5 và OpenCASCADE đã được cài đặt
- Xem log trong console để biết chi tiết lỗi

### Nếu mô hình không hiển thị
- File STEP có thể bị lỗi hoặc rỗng
- Thử tạo lại mô hình với mô tả khác
- Kiểm tra FreeCAD code có chạy thành công không

## Lưu ý kỹ thuật

### Yêu cầu hệ thống
- Python với PyQt5
- OpenCASCADE (OCC) libraries
- Card đồ họa hỗ trợ OpenGL

### File được hỗ trợ
- Định dạng chính: `.step`
- Vị trí file: `outputs/cad/YYYY-MM-DD/`
- Tự động chọn file mới nhất

### Performance
- Viewer tối ưu cho mô hình CAD cỡ vừa và nhỏ
- Hỗ trợ hardware acceleration qua OpenGL
- Memory usage tối ưu với lazy loading

## Troubleshooting

### Lỗi thường gặp
1. **"No 3D models found"**: Chưa có file STEP nào được tạo
2. **"3D Model Viewer not found"**: Thiếu file gui_step.py
3. **"Failed to start 3D Model Viewer"**: Lỗi dependencies hoặc permissions
4. **Viewer không xuất hiện sau khi đóng**: Cửa sổ có thể bị ẩn đằng sau

### Giải pháp
1. Tạo mô hình mới trước khi mở viewer
2. Kiểm tra cài đặt dependencies
3. Chạy với quyền administrator nếu cần
4. **Kiểm tra taskbar** - cửa sổ có thể đang chạy nhưng bị ẩn
5. **Sử dụng Alt+Tab** để tìm cửa sổ viewer
6. **Nhấn nút View 3D Model nhiều lần** - mỗi lần sẽ mở cửa sổ mới

### Cải thiện mới (v2.0)
- ✅ **Sửa lỗi cửa sổ không xuất hiện lại**: Sử dụng DETACHED_PROCESS trên Windows
- ✅ **Cửa sổ luôn hiển thị ở phía trước**: Tự động bring to foreground
- ✅ **Logging chi tiết**: Dễ dàng debug khi có vấn đề
- ✅ **Thông báo rõ ràng**: UX tốt hơn với success/error messages
- ✅ **Test scripts**: Có thể test chức năng độc lập

## Tính năng nâng cao

### Keyboard shortcuts (trong viewer)
- **F**: Fit view to model
- **Esc**: Deselect all
- **Space**: Reset view

### Mouse controls
- **Left click**: Select face/edge
- **Right click**: Context menu (future feature)
- **Double click**: Fit view to selection

---

**Lưu ý**: Tính năng này được tích hợp sẵn và hoạt động tự động. Bạn chỉ cần nhấn nút "View 3D Model" sau khi tạo mô hình thành công.
