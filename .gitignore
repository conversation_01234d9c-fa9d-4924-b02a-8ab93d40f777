# Environment variables
.env
.env.*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
.pytest_cache/

# Logs
*.log
logs/
error.log
api.log
server.log
start.log
logger.log

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Editor files
*~
.*.swp
.idea/
.vscode/
*.sublime-*

# Docker
.docker/

# Database
*.sqlite3
*.db

# Output directories
outputs/
temp_uploads/
data/chat_history.json

# Testing
test/
.coverage
htmlcov/

# Distribution / packaging
dist/
build/
outputs
