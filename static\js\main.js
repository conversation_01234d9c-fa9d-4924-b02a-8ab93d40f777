document.addEventListener("DOMContentLoaded", function () {
  const chatToggle = document.getElementById("chat-toggle");
  const chatbotWidget = document.getElementById("chatbot-widget");
  const closeChat = document.getElementById("close-chat");
  const minimizeChat = document.getElementById("minimize-chat");
  const chatForm = document.getElementById("chat-form");
  const userInput = document.getElementById("user-input");
  const codeOutput = document.getElementById("code-output"); // New element for code output
  const sendBtn = document.getElementById("send-btn");
  const refreshBtn = document.getElementById("refresh-btn"); // Added refresh button
  const editModeToggle = document.getElementById("edit-mode-toggle");
  const editModeIndicator = document.getElementById("edit-mode-indicator");
  const chatHistory = document.getElementById("chat-history"); // Chat history element
  const clearHistoryBtn = document.getElementById("clear-history-btn"); // Clear history button
  const attachFileBtn = document.getElementById("attach-file-btn"); // Unified attach file button
  const selectedFileInfo = document.getElementById("selected-file-info"); // Updated to handle both PDF and image files
  const viewStepBtn = document.getElementById("view-step-btn"); // STEP viewer button
  const sessionIndicator = document.getElementById("session-indicator"); // Session indicator
  const sessionIdDisplay = document.getElementById("session-id-display"); // Session ID display
  const charCount = document.getElementById("char-count"); // Character counter

  // Variable to store the latest generated code and selected files

  // Variable to store the latest generated code and selected files
  let latestCode = null;
  let isEditMode = false;
  let selectedPdfFile = null; // Store the selected PDF file
  let selectedImageFile = null; // Store the selected image file
  let currentSessionId = null; // Store the current session ID for conversation continuity
  let isProcessing = false; // Add global processing state

  // Direct style override for parameter requests
  const styleOverride = document.createElement("style");
  styleOverride.textContent = `
    #code-output pre {
      background-color: #e8f4ff !important;
      border-left: 3px solid #4299e1 !important;
      color: #2c5282 !important;
      padding: 8px 12px !important;
      border-radius: 8px !important;
      margin-bottom: 10px !important;
    }
  `;
  document.head.appendChild(styleOverride);
  console.log("Added direct style override for parameter requests");

  // Removed conversation variable and model-related elements/logic

  // Enhanced loading utilities
  function setGlobalLoading(loading, message = "Processing...") {
    isProcessing = loading;

    if (loading) {
      // Disable form elements
      userInput.disabled = true;
      sendBtn.disabled = true;
      attachFileBtn.disabled = true;
      refreshBtn.disabled = true;
      editModeToggle.disabled = true;

      // Add loading class to form
      chatForm.classList.add("processing");

      // Update send button with loading state
      sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';

      // Show global loading overlay
      showGlobalLoadingOverlay(message);
    } else {
      // Re-enable form elements
      userInput.disabled = false;
      sendBtn.disabled =
        userInput.value.trim() === "" && !selectedPdfFile && !selectedImageFile;
      attachFileBtn.disabled = false;
      refreshBtn.disabled = false;
      editModeToggle.disabled = false;

      // Remove loading class from form
      chatForm.classList.remove("processing");

      // Restore send button
      sendBtn.innerHTML = '<i class="fas fa-paper-plane text-sm"></i>';

      // Hide global loading overlay
      hideGlobalLoadingOverlay();
    }
  }

  function showGlobalLoadingOverlay(message) {
    // Remove existing overlay if any
    const existingOverlay = document.getElementById("global-loading-overlay");
    if (existingOverlay) {
      existingOverlay.remove();
    }

    const overlay = document.createElement("div");
    overlay.id = "global-loading-overlay";
    overlay.className =
      "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
    overlay.innerHTML = `
      <div class="bg-white rounded-xl p-8 max-w-sm mx-4 text-center shadow-2xl">
        <div class="relative mb-6">
          <div class="w-16 h-16 mx-auto">
            <div class="loading-spinner"></div>
          </div>
        </div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">Processing Request</h3>
        <p class="text-gray-600 text-sm mb-4">${message}</p>
        <div class="flex items-center justify-center space-x-1">
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
        </div>
      </div>
    `;

    document.body.appendChild(overlay);

    // Animate in
    setTimeout(() => {
      overlay.style.opacity = "1";
    }, 10);
  }

  function hideGlobalLoadingOverlay() {
    const overlay = document.getElementById("global-loading-overlay");
    if (overlay) {
      overlay.style.opacity = "0";
      setTimeout(() => {
        overlay.remove();
      }, 300);
    }
  }

  function showInlineLoading(element, message, type = "primary") {
    const loadingId = `loading-${Date.now()}`;
    const colorClasses = {
      primary: "bg-indigo-600",
      success: "bg-green-600",
      warning: "bg-yellow-600",
      danger: "bg-red-600",
    };

    const loadingIndicator = document.createElement("div");
    loadingIndicator.id = loadingId;
    loadingIndicator.className = "text-center p-4 loading-fade-in";
    loadingIndicator.innerHTML = `
      <div class="inline-flex items-center px-6 py-3 ${colorClasses[type]} text-white rounded-lg shadow-lg">
        <div class="loading-spinner-small mr-3"></div>
        <span class="font-medium">${message}</span>
      </div>
    `;

    element.appendChild(loadingIndicator);
    return loadingId;
  }

  function hideInlineLoading(loadingId) {
    const loading = document.getElementById(loadingId);
    if (loading) {
      loading.classList.add("loading-fade-out");
      setTimeout(() => {
        loading.remove();
      }, 300);
    }
  }

  // PDF upload and processing function
  function processPDF(file, additionalText = "") {
    console.log("Processing PDF:", file.name);

    // Set global loading state
    setGlobalLoading(true, "Analyzing PDF document...");

    // Create FormData object
    const formData = new FormData();
    formData.append("file", file);

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show inline loading indicator
    const loadingId = showInlineLoading(
      codeOutput,
      "Processing PDF document...",
      "primary"
    );

    // Call the API endpoint
    fetch("/api/process-pdf", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        if (data.success) {
          // Store session_id for conversation continuity
          if (data.session_id) {
            updateSessionIndicator(data.session_id);
          }

          // Display the analysis result
          displayMessage(data.message, "bot");
        } else {
          // Display error
          displayError(`Error processing PDF: ${data.message}`);
        }
      })
      .catch((error) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        console.error("Error:", error);
        displayError(`Error processing PDF: ${error.message}`);
      });
  }

  // Image upload and processing function
  function processImage(file, additionalText = "") {
    console.log("Processing Image:", file.name);

    // Set global loading state
    setGlobalLoading(true, "Analyzing image...");

    // Create FormData object
    const formData = new FormData();
    formData.append("file", file);

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show inline loading indicator
    const loadingId = showInlineLoading(
      codeOutput,
      "Processing image...",
      "success"
    );

    // Call the API endpoint
    fetch("/api/process-image", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        if (data.success) {
          // Store session_id for conversation continuity
          if (data.session_id) {
            updateSessionIndicator(data.session_id);
          }

          // Display the analysis result
          displayMessage(data.message, "bot");
        } else {
          // Display error
          displayError(`Error processing image: ${data.message}`);
        }
      })
      .catch((error) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        console.error("Error:", error);
        displayError(`Error processing image: ${error.message}`);
      });
  }

  // Multi-file processing function
  function processMultiFile(pdfFile, imageFile, additionalText = "") {
    console.log("Processing multiple files:", {
      pdf: pdfFile?.name,
      image: imageFile?.name,
    });

    // Set global loading state
    setGlobalLoading(true, "Processing multiple files...");

    // Create FormData object
    const formData = new FormData();

    if (pdfFile) {
      formData.append("pdf_file", pdfFile);
    }
    if (imageFile) {
      formData.append("image_file", imageFile);
    }

    // Add user input if provided
    if (additionalText) {
      formData.append("user_input", additionalText);
    }

    // Add current session_id if available for continuity
    if (currentSessionId) {
      formData.append("session_id", currentSessionId);
    }

    // Show inline loading indicator
    const loadingId = showInlineLoading(
      codeOutput,
      "Processing multiple files...",
      "warning"
    );

    // Call the API endpoint
    fetch("/api/process-multi-file", {
      method: "POST",
      body: formData,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        // Display results based on what was processed
        let message = "";

        if (data.pdf_result) {
          message += `PDF Analysis: ${
            data.pdf_result.success
              ? data.pdf_result.message
              : "Failed - " + data.pdf_result.message
          }\n\n`;
        }

        if (data.image_result) {
          message += `Image Analysis: ${
            data.image_result.success
              ? data.image_result.message
              : "Failed - " + data.image_result.message
          }\n\n`;
        }

        if (data.combined_result) {
          message += `Combined Analysis: ${data.combined_result.message}`;
        }

        if (message) {
          displayMessage(message.trim(), "bot");
        } else {
          displayError("No results received from file processing");
        }
      })
      .catch((error) => {
        // Remove loading indicator
        hideInlineLoading(loadingId);
        setGlobalLoading(false);

        console.error("Error:", error);
        displayError(`Error processing files: ${error.message}`);
      });
  }

  // Refresh button event listener (to reset state)
  refreshBtn.addEventListener("click", function () {
    console.log(
      "%c🔄 CLIENT: Refresh button pressed",
      "color: blue; font-weight: bold"
    );

    // Show loading state on refresh button
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';

    // Clear session
    updateSessionIndicator(null);

    // Clear code output area
    codeOutput.innerHTML = `
      <div class="flex flex-col items-center justify-center h-full text-gray-400">
        <i class="fas fa-robot text-4xl mb-4 text-gray-300"></i>
        <p class="text-center">Enter a description or upload an image to start generating CAD models</p>
        <p class="text-xs text-gray-400 mt-2">Supports: Text description, images (JPG, PNG), PDF files</p>
      </div>
    `;

    // Hide STEP viewer button
    viewStepBtn.classList.add("hidden");

    // Reset other state
    latestCode = null;
    selectedPdfFile = null;
    selectedImageFile = null;
    updateFileDisplay();

    // Call the refresh API
    fetch("/api/refresh_chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        if (!response.ok) {
          console.error(
            "%c❌ CLIENT: Server responded with error status: " +
              response.status,
            "color: red; font-weight: bold"
          );
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log(
          "%c✅ CLIENT: Chat refreshed successfully",
          "color: green; font-weight: bold"
        );
        console.log("Response data:", data);

        // Show success state briefly
        refreshBtn.innerHTML = '<i class="fas fa-check text-sm"></i>';
        setTimeout(() => {
          refreshBtn.innerHTML = originalContent;
          refreshBtn.disabled = false;
        }, 1000);
      })
      .catch((error) => {
        console.error(
          "%c❌ CLIENT: Error refreshing chat: " + error,
          "color: red; font-weight: bold"
        );

        // Show error state briefly
        refreshBtn.innerHTML =
          '<i class="fas fa-exclamation-triangle text-sm"></i>';
        setTimeout(() => {
          refreshBtn.innerHTML = originalContent;
          refreshBtn.disabled = false;
        }, 2000);
      });
  });

  // Unified file attachment button event listener
  attachFileBtn.addEventListener("click", function () {
    console.log("CLIENT: Attach File button pressed");
    // Create a hidden file input element
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = ".pdf,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.webp"; // Accept both PDF and image formats
    fileInput.style.display = "none";

    // Append to the body to make it interactable (though hidden)
    document.body.appendChild(fileInput);

    // Listen for file selection
    fileInput.addEventListener("change", function (event) {
      const file = event.target.files[0];
      if (file) {
        console.log("CLIENT: Selected file:", file.name, file.type, file.size);

        // Determine file type
        const isPDF =
          file.type === "application/pdf" ||
          file.name.toLowerCase().endsWith(".pdf");
        const isImage =
          file.type.startsWith("image/") ||
          /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i.test(file.name);

        if (!isPDF && !isImage) {
          displayError("Please select a valid PDF or image file.");
          document.body.removeChild(fileInput);
          return;
        }

        // Validate file size
        const maxSize = 20 * 1024 * 1024; // 20MB
        if (file.size > maxSize) {
          displayError(
            `File size (${(file.size / 1024 / 1024).toFixed(
              2
            )}MB) exceeds maximum allowed size (20MB)`
          );
          document.body.removeChild(fileInput);
          return;
        }

        // Store the file based on type
        if (isPDF) {
          selectedPdfFile = file;
        } else if (isImage) {
          selectedImageFile = file;
        }

        // Update file display
        updateFileDisplay();

        // Enable send button
        sendBtn.disabled = false;
        userInput.placeholder = "💬 Add comment or send now...";
      } else {
        userInput.placeholder =
          "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
      }
      // Clean up the input element after use
      document.body.removeChild(fileInput);
    });

    // Programmatically click the hidden file input
    fileInput.click();
  });

  // Function to update file display
  function updateFileDisplay() {
    let displayHTML = "";

    if (selectedPdfFile) {
      displayHTML += `
        <div class="flex items-center justify-between bg-indigo-50 border border-indigo-200 rounded-lg p-3 mb-2">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0 w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
              <i class="fas fa-file-pdf text-indigo-600"></i>
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-indigo-900 truncate">${
                selectedPdfFile.name
              }</p>
              <p class="text-xs text-indigo-600">${(
                selectedPdfFile.size /
                1024 /
                1024
              ).toFixed(2)} MB • PDF Document</p>
            </div>
          </div>
          <button type="button" class="remove-file-btn flex-shrink-0 w-6 h-6 bg-indigo-200 hover:bg-indigo-300 rounded-full flex items-center justify-center transition-colors" data-file-type="pdf">
            <i class="fas fa-times text-indigo-600 text-xs"></i>
          </button>
        </div>`;
    }

    if (selectedImageFile) {
      // Create image preview
      const imageUrl = URL.createObjectURL(selectedImageFile);
      displayHTML += `
        <div class="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3 mb-2">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <img src="${imageUrl}" alt="Preview" class="w-10 h-10 object-cover rounded-lg border border-green-200">
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-green-900 truncate">${
                selectedImageFile.name
              }</p>
              <p class="text-xs text-green-600">${(
                selectedImageFile.size / 1024
              ).toFixed(1)} KB • ${selectedImageFile.type}</p>
            </div>
          </div>
          <button type="button" class="remove-file-btn flex-shrink-0 w-6 h-6 bg-green-200 hover:bg-green-300 rounded-full flex items-center justify-center transition-colors" data-file-type="image">
            <i class="fas fa-times text-green-600 text-xs"></i>
          </button>
        </div>`;
    }

    // Show/hide file info section
    if (displayHTML) {
      selectedFileInfo.innerHTML = displayHTML;
      selectedFileInfo.classList.remove("empty:hidden");
    } else {
      selectedFileInfo.innerHTML = "";
      selectedFileInfo.classList.add("empty:hidden");
    }

    // Add event listeners for remove buttons
    const removeButtons = selectedFileInfo.querySelectorAll(".remove-file-btn");
    removeButtons.forEach((button) => {
      button.addEventListener("click", function () {
        const fileType = this.getAttribute("data-file-type");

        // Clean up object URL to prevent memory leaks
        if (fileType === "image" && selectedImageFile) {
          const imageElements = selectedFileInfo.querySelectorAll("img");
          imageElements.forEach((img) => {
            if (img.src.startsWith("blob:")) {
              URL.revokeObjectURL(img.src);
            }
          });
          selectedImageFile = null;
        } else if (fileType === "pdf") {
          selectedPdfFile = null;
        }

        updateFileDisplay();

        // Update UI state
        if (!selectedPdfFile && !selectedImageFile) {
          userInput.placeholder =
            "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
          sendBtn.disabled = userInput.value.trim() === "";
        }
      });
    });
  }

  // Enhanced input handling with character count and UI updates
  userInput.addEventListener("input", function () {
    // Update character count
    const length = this.value.length;
    if (charCount) {
      charCount.textContent = length;
    }

    // Update send button state - but not if processing
    if (!isProcessing) {
      sendBtn.disabled =
        this.value.trim() === "" && !selectedPdfFile && !selectedImageFile;
    }

    // Auto-resize textarea
    this.style.height = "auto";
    this.style.height = Math.min(this.scrollHeight, 200) + "px";

    // Add subtle animation for character count
    if (charCount) {
      charCount.style.transform = "scale(1.1)";
      setTimeout(() => {
        charCount.style.transform = "scale(1)";
      }, 150);
    }
  });

  // Enhanced notification system
  function showNotification(message, type = "info", duration = 4000) {
    const notification = document.createElement("div");
    const notificationId = `notification-${Date.now()}`;
    notification.id = notificationId;

    const typeStyles = {
      success: "bg-green-50 border-green-200 text-green-800",
      error: "bg-red-50 border-red-200 text-red-800",
      warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
      info: "bg-blue-50 border-blue-200 text-blue-800",
    };

    const icons = {
      success: "fa-check-circle",
      error: "fa-exclamation-circle",
      warning: "fa-exclamation-triangle",
      info: "fa-info-circle",
    };

    notification.className = `fixed top-4 right-4 max-w-sm p-4 border rounded-lg shadow-lg z-50 ${typeStyles[type]} notification-enter`;
    notification.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <i class="fas ${icons[type]} text-lg"></i>
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium">${message}</p>
        </div>
        <div class="ml-4 flex-shrink-0">
          <button class="notification-close inline-flex text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closeNotification('${notificationId}')">
            <i class="fas fa-times text-sm"></i>
          </button>
        </div>
      </div>
      <div class="notification-progress"></div>
    `;

    document.body.appendChild(notification);

    // Auto-remove after duration
    setTimeout(() => {
      closeNotification(notificationId);
    }, duration);

    return notificationId;
  }

  // Global function to close notifications
  window.closeNotification = function (notificationId) {
    const notification = document.getElementById(notificationId);
    if (notification) {
      notification.classList.add("notification-exit");
      setTimeout(() => {
        notification.remove();
      }, 300);
    }
  };

  // Enhanced error display function
  function displayError(error) {
    const errorDiv = document.createElement("div");
    errorDiv.className =
      "bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded loading-fade-in error-shake";

    // Create a heading for the error
    const errorHeading = document.createElement("div");
    errorHeading.className = "font-bold flex items-center";
    errorHeading.innerHTML =
      '<i class="fas fa-exclamation-triangle mr-2"></i>Error';
    errorDiv.appendChild(errorHeading);

    // Add the error message
    const errorMessage = document.createElement("div");
    errorMessage.className = "mt-2";
    errorMessage.textContent = error;
    errorDiv.appendChild(errorMessage);

    // Add to code output
    codeOutput.appendChild(errorDiv);
    errorDiv.scrollIntoView({ behavior: "smooth" });

    // Show notification
    showNotification(error, "error", 6000);

    // Log the error to console for debugging
    console.error("Error displayed to user:", error);
  }

  // Show/hide paste hint on focus
  userInput.addEventListener("focus", function () {
    console.log("📝 Input focused - paste capability active");

    // Show paste hint if no content
  });

  userInput.addEventListener("blur", function () {
    // Hide paste hint
  });

  // Add paste event listener for image pasting - attach to both textarea and document
  function handleImagePaste(e) {
    console.log("🔄 PASTE EVENT DETECTED!");
    console.log("- Target:", e.target.tagName, e.target.id);
    console.log("- Event type:", e.type);
    console.log("- Has clipboardData:", !!e.clipboardData);

    // Check if clipboard contains data
    if (!e.clipboardData) {
      console.log("❌ No clipboardData available");
      return;
    }

    const items = e.clipboardData.items;
    const files = e.clipboardData.files;

    console.log("📋 Clipboard contents:");
    console.log("- Items count:", items ? items.length : 0);
    console.log("- Files count:", files ? files.length : 0);

    // Debug all clipboard items
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        console.log(`  Item ${i}:`, {
          kind: item.kind,
          type: item.type,
          hasFile: !!item.getAsFile,
        });
      }
    }

    // Check for files in clipboardData.files first
    if (files && files.length > 0) {
      console.log("📁 Found files in clipboard.files");
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`File ${i}:`, file.name, file.type, file.size);

        if (file.type.startsWith("image/")) {
          console.log("✅ Processing image file from clipboard.files");
          processClipboardImage(file, e);
          return;
        }
      }
    }

    // Check for image items
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        // Check if the item is an image
        if (item.kind === "file" && item.type.indexOf("image") !== -1) {
          console.log("✅ Image found in clipboard items:", item.type);

          // Prevent default paste behavior
          e.preventDefault();

          // Get the image file from clipboard
          const file = item.getAsFile();

          if (file) {
            console.log(
              "📸 Successfully got image file:",
              file.name,
              file.size
            );
            processClipboardImage(file, e);
            return;
          } else {
            console.log("❌ Failed to get file from clipboard item");
          }
        }
      }
    }

    // If no image found, check for text that might be a data URL
    if (items) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.kind === "string" && item.type === "text/plain") {
          item.getAsString((text) => {
            if (text.startsWith("data:image/")) {
              console.log(
                "📝 Found data URL in text:",
                text.substring(0, 50) + "..."
              );
              // Convert data URL to file
              convertDataURLToFile(text, e);
            }
          });
        }
      }
    }

    console.log("❌ No image found in clipboard");

    // Show a helpful message to user if no image was found
  }

  // Enhanced file processing with better notifications
  function processClipboardImage(file, event) {
    console.log("🖼️ Processing clipboard image:", {
      name: file.name || "clipboard-image",
      type: file.type,
      size: file.size,
    });

    // Validate file size
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
      const errorMsg = `Image size (${(file.size / 1024 / 1024).toFixed(
        2
      )}MB) exceeds maximum allowed size (20MB)`;
      displayError(errorMsg);
      showNotification(errorMsg, "error");
      return;
    }

    // Create a proper filename for the pasted image
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileExtension = file.type.split("/")[1] || "png";
    const fileName = file.name || `pasted-image-${timestamp}.${fileExtension}`;

    // Create a new File object with proper name
    const namedFile = new File([file], fileName, { type: file.type });

    // Store as selected image file
    selectedImageFile = namedFile;
    console.log("✅ Image stored successfully:", fileName);

    // Update file display
    updateFileDisplay();

    // Enable send button and update placeholder
    if (!isProcessing) {
      sendBtn.disabled = false;
    }
    userInput.placeholder = "Add comment or send";

    // Show success notification
    showNotification(`Image pasted successfully: ${fileName}`, "success", 3000);

    console.log("🎉 Image paste process completed successfully!");
  }

  // Function to convert data URL to file
  function convertDataURLToFile(dataURL, event) {
    try {
      const arr = dataURL.split(",");
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const fileExtension = mime.split("/")[1] || "png";
      const fileName = `pasted-image-${timestamp}.${fileExtension}`;

      const file = new File([u8arr], fileName, { type: mime });

      event.preventDefault();
      processClipboardImage(file, event);
    } catch (error) {
      console.error("❌ Error converting data URL to file:", error);
    }
  }

  // Attach paste event to textarea
  userInput.addEventListener("paste", handleImagePaste);

  // Also attach to the form container for better coverage
  chatForm.addEventListener("paste", handleImagePaste);

  // Add global paste listener as fallback
  document.addEventListener("paste", function (e) {
    // Only handle if the focus is within our chat area or no specific target
    const isInChatArea =
      chatForm.contains(e.target) ||
      e.target === userInput ||
      e.target === document.body ||
      e.target === document;

    if (isInChatArea) {
      console.log(
        "🌐 Global paste event triggered for target:",
        e.target.tagName
      );
      handleImagePaste(e);
    }
  });

  // Enhanced drag and drop functionality
  function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    chatForm.classList.add("drag-over");
  }

  function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    // Only remove class if we're leaving the form entirely
    if (!chatForm.contains(e.relatedTarget)) {
      chatForm.classList.remove("drag-over");
    }
  }

  // Enhanced drag and drop with better feedback
  function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    chatForm.classList.remove("drag-over");

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      console.log("🗂️ File dropped:", file.name, file.type, file.size);

      // Check if it's an image
      const isImage =
        file.type.startsWith("image/") ||
        /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i.test(file.name);
      const isPDF =
        file.type === "application/pdf" ||
        file.name.toLowerCase().endsWith(".pdf");

      if (!isImage && !isPDF) {
        const errorMsg = "Please select a valid image or PDF file.";
        displayError(errorMsg);
        showNotification(errorMsg, "error");
        return;
      }

      // Validate file size
      const maxSize = 20 * 1024 * 1024; // 20MB
      if (file.size > maxSize) {
        const errorMsg = `File size (${(file.size / 1024 / 1024).toFixed(
          2
        )}MB) exceeds maximum allowed size (20MB)`;
        displayError(errorMsg);
        showNotification(errorMsg, "error");
        return;
      }

      // Store the file
      if (isImage) {
        selectedImageFile = file;
      } else if (isPDF) {
        selectedPdfFile = file;
      }

      // Update file display
      updateFileDisplay();

      // Enable send button and update placeholder
      if (!isProcessing) {
        sendBtn.disabled = false;
      }
      userInput.placeholder = "💬 Add comment or send now...";

      // Show enhanced success notification
      const fileType = isImage ? "Image" : "PDF";
      showNotification(
        `${fileType} file dropped successfully: ${file.name}`,
        "success"
      );
    }
  }

  // Add drag and drop event listeners
  chatForm.addEventListener("dragover", handleDragOver);
  chatForm.addEventListener("dragleave", handleDragLeave);
  chatForm.addEventListener("drop", handleDrop);

  // Also add to the entire form container for better coverage
  const formContainer = chatForm.parentElement;
  if (formContainer) {
    formContainer.addEventListener("dragover", handleDragOver);
    formContainer.addEventListener("dragleave", handleDragLeave);
    formContainer.addEventListener("drop", handleDrop);
  }

  // Add visual feedback for paste capability
  userInput.addEventListener("focus", function () {
    console.log("📝 Input focused - paste capability active");
  });

  // Add keyboard shortcut hint
  userInput.addEventListener("keydown", function (e) {
    if (e.ctrlKey && e.key === "v") {
      console.log("🔄 Ctrl+V detected - waiting for paste event");
    }
  });

  // Enhanced test function to check paste capability (for debugging)
  window.testPasteCapability = function () {
    console.log("🧪 Testing paste capability...");
    console.log("- userInput element:", userInput);
    console.log("- chatForm element:", chatForm);
    console.log("- Event listeners attached");

    // Test if we can access clipboard
    if (navigator.clipboard) {
      console.log("✅ Clipboard API available");

      // Try to read clipboard if possible
      navigator.clipboard
        .read()
        .then((clipboardItems) => {
          console.log("📋 Current clipboard items:", clipboardItems.length);
          clipboardItems.forEach((item, i) => {
            console.log(`Item ${i} types:`, item.types);
          });
        })
        .catch((err) => {
          console.log(
            "⚠️ Cannot read clipboard (permission required):",
            err.message
          );
        });
    } else {
      console.log("❌ Clipboard API not available");
    }

    console.log("💡 To test: Copy an image and paste it in the textarea");
    console.log("🎯 Watch for console messages starting with 🔄 when pasting");
  };

  // Development mode - only show test buttons in development
  if (
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1"
  ) {
    console.log("🛠️ Development mode detected - showing debug tools");

    // Add minimal debug button
    const debugButton = document.createElement("button");
    debugButton.textContent = "🧪 Debug";
    debugButton.className =
      "fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded shadow-lg text-sm z-50 opacity-75 hover:opacity-100 transition-opacity";
    debugButton.onclick = () => {
      testPasteCapability();
      console.log("📋 Clipboard test - check console for details");
    };
    document.body.appendChild(debugButton);
  }

  // Toggle chat widget
  chatToggle.addEventListener("click", function () {
    chatbotWidget.classList.toggle("active");
    chatToggle.classList.toggle("hidden");
  });

  // Close chat
  closeChat.addEventListener("click", function () {
    chatbotWidget.classList.remove("active");
    chatToggle.classList.remove("hidden");
  });

  // Minimize chat
  minimizeChat.addEventListener("click", function () {
    chatbotWidget.classList.remove("active");
    chatToggle.classList.remove("hidden");
  });

  // Edit mode toggle
  editModeToggle.addEventListener("change", function () {
    isEditMode = this.checked;
    console.log(
      `🎯 [EDIT MODE DEBUG] Edit mode toggled: ${isEditMode ? "ON" : "OFF"}`
    );
    console.log(
      `📋 [EDIT MODE DEBUG] Current session ID: ${currentSessionId || "None"}`
    );
    console.log(
      `💾 [EDIT MODE DEBUG] Latest code available: ${latestCode ? "Yes" : "No"}`
    );

    // Update debug panel
    updateDebugPanel();

    if (isEditMode) {
      editModeIndicator.classList.remove("hidden");
      userInput.placeholder = "Enter edit request...";

      // Disable edit mode if no code has been generated yet
      if (!latestCode) {
        // Check if we have a current session to get code from
        if (currentSessionId) {
          console.log(
            `🔍 [EDIT MODE DEBUG] Fetching latest code for session: ${currentSessionId}`
          );

          // Try to get the latest code for the current session
          fetch(`/api-production/sessions/${currentSessionId}/latest-code`)
            .then((response) => {
              console.log(
                `📡 [EDIT MODE DEBUG] API response status: ${response.status}`
              );
              if (response.ok) {
                return response.json();
              } else {
                throw new Error(
                  `Failed to get latest code: ${response.status}`
                );
              }
            })
            .then((data) => {
              console.log(`📋 [EDIT MODE DEBUG] Received data:`, data);

              if (data.latest_code) {
                console.log(
                  `✅ [EDIT MODE DEBUG] Found code for session ${data.session_id}, length: ${data.latest_code.length} characters`
                );
                console.log(
                  `🔍 [EDIT MODE DEBUG] Code preview: ${data.latest_code.substring(
                    0,
                    100
                  )}...`
                );

                // Verify session ID matches
                if (data.session_id !== currentSessionId) {
                  console.error(
                    `❌ [EDIT MODE DEBUG] SESSION MISMATCH! Expected: ${currentSessionId}, Got: ${data.session_id}`
                  );
                  alert(
                    `ERROR: Session mismatch!\nExpected: ${currentSessionId}\nReceived: ${data.session_id}`
                  );
                  return;
                }

                // Use the latest code from current session
                displayCode(data.latest_code);
                latestCode = data.latest_code;

                // Update debug panel
                updateDebugPanel();

                // Show notification with session info
                const notification = document.createElement("div");
                notification.className =
                  "text-sm text-green-600 mt-1 mb-2 fade-out";
                notification.textContent = `Loaded latest code from session ${data.session_id} for editing.`;
                notification.style.animation = "fadeOut 3s forwards";

                // Add the notification before the form
                chatForm.parentNode.insertBefore(notification, chatForm);

                // Remove the notification after 3 seconds
                setTimeout(() => {
                  notification.remove();
                }, 3000);
              } else {
                console.log(
                  `❌ [EDIT MODE DEBUG] No code found for session ${currentSessionId}`
                );
                // No code in current session, disable edit mode
                alert(
                  "No code has been generated in this session yet. Generate code first before using edit mode."
                );
                editModeToggle.checked = false;
                isEditMode = false;
                editModeIndicator.classList.add("hidden");
                userInput.placeholder = "Enter CAD description...";
              }
            })
            .catch((error) => {
              console.error(
                "Error loading latest code for current session:",
                error
              );
              // Fallback: disable edit mode
              alert(
                "No code has been generated in this session yet. Generate code first before using edit mode."
              );
              editModeToggle.checked = false;
              isEditMode = false;
              editModeIndicator.classList.add("hidden");
              userInput.placeholder =
                "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
            });
        } else {
          // No current session, disable edit mode
          alert(
            "No active session found. Start a conversation first before using edit mode."
          );
          editModeToggle.checked = false;
          isEditMode = false;
          editModeIndicator.classList.add("hidden");
          userInput.placeholder =
            "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
        }
      } else {
        // Show a notification about automatic bounding box detection
        const notification = document.createElement("div");
        notification.className = "text-sm text-gray-600 mt-1 mb-2 fade-out"; // Use fade-out class
        notification.textContent =
          "Edit mode enabled. Bounding box will be detected automatically if not specified.";
        notification.style.animation = "fadeOut 3s forwards"; // Add fade-out animation

        // Add the notification before the form
        chatForm.parentNode.insertBefore(notification, chatForm);

        // Remove the notification after 3 seconds
        setTimeout(() => {
          notification.remove();
        }, 3000);
      }
    } else {
      editModeIndicator.classList.add("hidden");
      userInput.placeholder =
        "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
    }
  });

  // Modify Form submission to handle multiple file types
  chatForm.addEventListener("submit", function (e) {
    e.preventDefault();
    const message = userInput.value.trim();

    // Prevent submission if already processing
    if (isProcessing) {
      return;
    }

    // Check if files are selected
    if (selectedPdfFile || selectedImageFile) {
      // Determine which processing function to use
      if (selectedPdfFile && selectedImageFile) {
        // Process both files together
        processMultiFile(selectedPdfFile, selectedImageFile, message);
      } else if (selectedPdfFile) {
        // Process PDF only
        processPDF(selectedPdfFile, message);
      } else if (selectedImageFile) {
        // Process image only
        processImage(selectedImageFile, message);
      }

      // Reset file selections
      selectedFileInfo.innerHTML = "";
      selectedPdfFile = null;
      selectedImageFile = null;

      // Clear input field
      userInput.value = "";
      userInput.placeholder =
        "Enter CAD description or paste image (Ctrl+V), or drag and drop file here...";
      sendBtn.disabled = true;

      return; // Return early to avoid standard text processing
    }

    if (message) {
      // Set global loading state
      setGlobalLoading(
        true,
        isEditMode ? "Modifying CAD model..." : "Generating CAD model..."
      );

      // Clear the input field immediately after getting the message
      userInput.value = "";

      // Clear previous code output
      codeOutput.innerHTML = "";

      // Show enhanced typing indicator
      showEnhancedTypingIndicator();

      // Send request to API
      fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: message,
          is_edit_request: isEditMode,
          session_id: currentSessionId, // Include session_id for conversation continuity
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          // Remove typing indicator and global loading
          removeEnhancedTypingIndicator();
          setGlobalLoading(false);

          console.log("Server response:", data); // Log the response for debugging

          // Store session_id from chat response for continuity
          if (data.session_id) {
            // Check if session_id exists before updating
            updateSessionIndicator(data.session_id);
          }

          // The primary content from the server is in data.chat_response
          if (data.chat_response) {
            displayMessage(data.chat_response, "bot"); // This will handle parameter requests, success messages, etc.

            // If the response indicates successful generation (e.g., by providing export links),
            // make the "View STEP" button visible.
            // The actual FreeCAD code is not sent in this response, so `latestCode` is not updated here.
            // `displayCode` is not called here for new code.
            if (data.obj_export || data.step_export) {
              console.log(
                "Export files available, ensuring STEP viewer button is visible."
              );
              if (viewStepBtn) {
                // Ensure viewStepBtn is defined and accessible
                viewStepBtn.classList.remove("hidden");
              }
            }
          }
          // If data.chat_response is missing, then the server's response format is truly unexpected
          // according to the ChatResponse schema where chat_response is a required field.
          else {
            console.warn(
              "Unexpected response format: data.chat_response is missing.",
              data
            );
            displayError(
              "Received an unexpected response format from the server (chat_response is missing)."
            );
          }

          sendBtn.disabled = false;
        })
        .catch((error) => {
          console.error("Error:", error);
          removeEnhancedTypingIndicator();
          setGlobalLoading(false);

          // Provide more detailed error information
          let errorMessage =
            "Sorry, an error occurred while connecting to the server.";
          if (error.message) {
            errorMessage += ` Details: ${error.message}`;
          }

          displayError(errorMessage);
          sendBtn.disabled = false;
        });
    }
  });

  // Enhanced typing indicator
  function showEnhancedTypingIndicator() {
    const typingDiv = document.createElement("div");
    typingDiv.className = "message flex space-x-2 loading-fade-in";
    typingDiv.id = "typing-indicator";
    typingDiv.innerHTML = `
      <div class="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
        <div class="typing-robot-icon">
          <i class="fas fa-robot text-white text-sm"></i>
        </div>
      </div>
      <div class="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-lg max-w-[80%] shadow-sm border">
        <div class="flex items-center space-x-2 mb-2">
          <span class="text-sm font-medium text-gray-700">AI Assistant</span>
          <span class="text-xs text-gray-500">is thinking...</span>
        </div>
        <div class="enhanced-typing-indicator">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>
    `;

    // Append to code output area instead of chat messages
    codeOutput.appendChild(typingDiv);
    typingDiv.scrollIntoView({ behavior: "smooth" });
  }

  // Remove enhanced typing indicator
  function removeEnhancedTypingIndicator() {
    const typingIndicator = document.getElementById("typing-indicator");
    if (typingIndicator) {
      typingIndicator.classList.add("loading-fade-out");
      setTimeout(() => {
        typingIndicator.remove();
      }, 300);
    }
  }

  // New function to display generated code
  function displayCode(code) {
    // Clear empty state if it exists
    clearEmptyState();

    const codeBlock = document.createElement("pre");
    const codeElement = document.createElement("code");

    // Check if this is a parameter request and style it accordingly
    if (
      code.includes("I need some more information") ||
      code.includes("Please provide the following parameters") ||
      code.includes("Sheet length") ||
      code.includes("Hole type")
    ) {
      console.log(
        "Detected parameter request in displayCode, applying special styling"
      );
      codeBlock.style.backgroundColor = "#e8f4ff";
      codeBlock.style.borderLeft = "3px solid #4299e1";
      codeBlock.style.color = "#2c5282";
      codeBlock.style.padding = "8px 12px";
      codeBlock.style.borderRadius = "8px";
      codeBlock.style.maxWidth = "100%";
      codeBlock.style.marginBottom = "10px";
      codeBlock.classList.add("parameter-request");
    }

    codeElement.textContent = code;
    codeBlock.appendChild(codeElement);
    codeOutput.appendChild(codeBlock);
    codeBlock.scrollIntoView({ behavior: "smooth" });

    // Show STEP viewer button if this is actual FreeCAD code (not parameter request)
    if (
      !code.includes("I need some more information") &&
      !code.includes("Please provide the following parameters") &&
      code.includes("import FreeCAD")
    ) {
      console.log("Showing STEP viewer button - FreeCAD code detected");
      viewStepBtn.classList.remove("hidden");
    }
  }

  // Function to clear empty state
  function clearEmptyState() {
    const emptyState = codeOutput.querySelector(
      ".flex.flex-col.items-center.justify-center"
    );
    if (emptyState) {
      emptyState.remove();
    }
  }

  // Chat history functions

  // Function to load chat history
  function loadChatHistory() {
    // Clear previous history
    chatHistory.innerHTML = "";

    // Show loading indicator
    const loadingIndicator = document.createElement("div");
    loadingIndicator.className = "text-center text-gray-500 py-2";
    loadingIndicator.textContent = "Loading chat history...";
    chatHistory.appendChild(loadingIndicator);

    // Fetch chat history from API
    fetch("/api/chat-history")
      .then((response) => response.json())
      .then((data) => {
        // Remove loading indicator
        chatHistory.innerHTML = "";

        // Check if history exists
        if (data.history && data.history.length > 0) {
          // Display history entries
          data.history.forEach((entry) => {
            displayHistoryEntry(entry);
          });
        } else {
          // Display empty message
          const emptyMessage = document.createElement("p");
          emptyMessage.className = "text-gray-400";
          emptyMessage.textContent = "No chat history found.";
          chatHistory.appendChild(emptyMessage);
        }
      })
      .catch((error) => {
        console.error("Error loading chat history:", error);
        chatHistory.innerHTML = "";
        const errorMessage = document.createElement("p");
        errorMessage.className = "text-red-500";
        errorMessage.textContent = "Failed to load chat history.";
        chatHistory.appendChild(errorMessage);
      });
  }

  // Function to display a history entry
  function displayHistoryEntry(entry) {
    const entryDiv = document.createElement("div");
    entryDiv.className = "mb-4 p-3 bg-white rounded shadow-sm";

    // Format timestamp
    const timestamp = new Date(entry.timestamp);
    const formattedDate = timestamp.toLocaleDateString();
    const formattedTime = timestamp.toLocaleTimeString();

    // Create entry content
    entryDiv.innerHTML = `
      <div class="flex justify-between items-start mb-2">
        <div class="font-medium ${
          entry.is_edit_request ? "text-indigo-600" : "text-gray-700"
        }">
          ${
            entry.is_edit_request
              ? '<i class="fas fa-pencil-alt mr-1"></i> Edit Request'
              : '<i class="fas fa-comment mr-1"></i> New Shape'
          }
        </div>
        <div class="text-xs text-gray-500">${formattedDate} ${formattedTime}</div>
      </div>
      <div class="text-sm text-gray-600 mb-2">${entry.user_message}</div>
      <div class="text-xs text-gray-500 flex justify-end">
        <button class="use-code-btn text-indigo-600 hover:text-indigo-800 transition">
          <i class="fas fa-code mr-1"></i> Use This Code
        </button>
      </div>
    `;

    // Add event listener to the "Use This Code" button
    const useCodeBtn = entryDiv.querySelector(".use-code-btn");
    useCodeBtn.addEventListener("click", function () {
      // Display the code in the code output area
      displayCode(entry.generated_code);
      // Store the code as the latest code
      latestCode = entry.generated_code;

      // Send a request to update the server-side latest code
      fetch("/api/update-latest-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code: entry.generated_code,
          session_id: currentSessionId, // Include session_id to update database
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            console.log("Latest code updated successfully:", data.message);
          } else {
            console.warn("Failed to update latest code:", data.message);
          }
        })
        .catch((error) => console.error("Error updating latest code:", error));

      // Enable edit mode automatically
      if (!isEditMode) {
        editModeToggle.checked = true;
        isEditMode = true;
        editModeIndicator.classList.remove("hidden");
        userInput.placeholder = "Enter edit request...";
      }

      // Show notification
      const notification = document.createElement("div");
      notification.className = "text-sm text-green-600 mt-1 mb-2 fade-out";
      notification.textContent =
        "Code loaded from history. Edit mode enabled for modifications.";
      notification.style.animation = "fadeOut 3s forwards";

      // Add the notification before the code output
      codeOutput.parentNode.insertBefore(notification, codeOutput);

      // Remove the notification after 3 seconds
      setTimeout(() => {
        notification.remove();
      }, 3000);

      // Focus on the input field for immediate editing
      userInput.focus();
    });

    // Add the entry to the chat history
    chatHistory.appendChild(entryDiv);
  }

  // Function to clear chat history
  function clearChatHistory() {
    // Show confirmation dialog
    if (
      confirm(
        "Are you sure you want to clear all chat history? This action cannot be undone."
      )
    ) {
      // Send request to API
      fetch("/api/chat-history", {
        method: "DELETE",
      })
        .then((response) => response.json())
        .then((data) => {
          // Reload chat history
          loadChatHistory();

          // Show notification
          const notification = document.createElement("div");
          notification.className = "text-sm text-green-600 mt-1 mb-2 fade-out";
          notification.textContent = "Chat history cleared successfully.";
          notification.style.animation = "fadeOut 3s forwards";

          // Add the notification before the chat history
          chatHistory.parentNode.insertBefore(notification, chatHistory);

          // Remove the notification after 3 seconds
          setTimeout(() => {
            notification.remove();
          }, 3000);
        })
        .catch((error) => {
          console.error("Error clearing chat history:", error);
          alert("Failed to clear chat history. Please try again.");
        });
    }
  }

  // Add event listener to clear history button
  clearHistoryBtn.addEventListener("click", clearChatHistory);

  // Load chat history when the page loads
  loadChatHistory();

  // Update loadChatHistory after successful chat submission
  const originalFetch = window.fetch;
  window.fetch = function () {
    const result = originalFetch.apply(this, arguments);

    // Check if this is a chat API call
    if (arguments[0] === "/api/chat" && arguments[1]?.method === "POST") {
      result.then((response) => {
        // Reload chat history after a short delay to ensure the new entry is saved
        setTimeout(loadChatHistory, 1000);
        return response;
      });
    }

    return result;
  };

  // Removed saved shapes logic
  // Function to display messages
  function displayMessage(message, sender) {
    // Clear empty state if it exists
    clearEmptyState();

    // Create a new message element
    const messageElement = document.createElement("div");
    messageElement.className = `message ${sender}`;

    // Check if this is a message asking for parameters
    // More robust detection of parameter request messages
    if (sender === "bot") {
      console.log("Creating bot message with class:", messageElement.className); // Debug log
      // Apply bot styles directly
      messageElement.style.backgroundColor = "#e8f4ff";
      messageElement.style.borderLeft = "3px solid #4299e1";
      messageElement.style.color = "#2c5282";
      messageElement.style.padding = "8px 12px";
      messageElement.style.borderRadius = "8px";
      messageElement.style.maxWidth = "90%";
      messageElement.style.marginBottom = "10px";
      // Extract questions from the message if they're in a list format
      const questions = [];
      const lines = message.split("\n");

      // Look for patterns that indicate this is a parameter request
      const isParameterRequest =
        (message.includes("missing") &&
          (message.includes("parameter") || message.includes("information"))) ||
        (message.includes("provide") && message.includes("following")) ||
        (message.includes("need") &&
          (message.includes("information") || message.includes("parameter"))) ||
        (message.includes("sheet") && message.includes("thickness")) ||
        (message.includes("hole") &&
          (message.includes("radius") || message.includes("pattern")));

      // Count how many numbered list items we have
      let numberedItems = 0;

      for (const line of lines) {
        // Look for numbered list items (e.g., "1. What is the length?")
        const match = line.match(/^\s*\d+\.\s+(.+)$/);
        if (match) {
          questions.push(match[1].trim());
          numberedItems++;
        }
      }

      // Special case for the exact format shown in the screenshot
      if (
        message.includes("Please provide the following missing information") &&
        (message.includes("Sheet thickness") ||
          message.includes("thickness")) &&
        (message.includes("Hole radius") || message.includes("radius")) &&
        (message.includes("Hole pattern") || message.includes("pattern"))
      ) {
        console.log("Detected specific perforated sheet parameter request");

        // Extract explanation if present
        let explanation = "";
        if (
          message.includes("Need") &&
          message.includes("to model the sheet")
        ) {
          const lines = message.split("\n");
          for (const line of lines) {
            if (line.includes("Need") && line.includes("to model the sheet")) {
              explanation = line.trim();
              break;
            }
          }
        }

        // Display the parameter table
        displayParameterTable([
          "Sheet thickness",
          "Hole radius",
          "Hole pattern",
        ]);

        // If we found an explanation, display it separately
        if (explanation) {
          setTimeout(() => {
            displayMessage(`<i>${explanation}</i>`, "bot-explanation");
          }, 500);
        }

        return;
      }

      // If we have a parameter request message with numbered items, display as a table
      if ((isParameterRequest || numberedItems >= 2) && questions.length > 0) {
        console.log(
          "Detected parameter request, displaying table with questions:",
          questions
        );
        displayParameterTable(questions);
        return;
      }
    }

    // Format the message with line breaks (for non-parameter messages)
    messageElement.innerHTML = message.replace(/\n/g, "<br>");

    // Add to code output container
    codeOutput.appendChild(messageElement);

    // Also add to chat messages container for compatibility
    const chatContainer = document.getElementById("chat-messages");
    if (chatContainer) {
      const chatMessageElement = messageElement.cloneNode(true);
      chatContainer.appendChild(chatMessageElement);
    }

    // Force style refresh (try to trigger reflow)
    messageElement.offsetHeight;

    // Scroll to the new message
    messageElement.scrollIntoView({ behavior: "smooth" });
  }

  // Function to display a table of missing parameters
  function displayParameterTable(questions) {
    console.log("Creating parameter table with questions:", questions);

    // Instead of creating a parameter table, display as normal chat messages
    const messageElement = document.createElement("div");
    messageElement.className = "message bot parameter-message";

    // Apply bot styles directly to ensure they take effect
    messageElement.style.backgroundColor = "#e8f4ff";
    messageElement.style.borderLeft = "3px solid #4299e1";
    messageElement.style.color = "#2c5282";
    messageElement.style.padding = "8px 12px";
    messageElement.style.borderRadius = "8px";
    messageElement.style.maxWidth = "90%";
    messageElement.style.marginBottom = "10px";

    // Create a list of questions
    let messageContent =
      "I need some more information. Please provide the following parameters:<br><ul>";
    questions.forEach((question) => {
      messageContent += `<li>${question}</li>`;
    });
    messageContent += "</ul>";

    messageElement.innerHTML = messageContent;
    codeOutput.appendChild(messageElement);

    // Log for debugging
    console.log("Added parameter message with styles:", {
      backgroundColor: messageElement.style.backgroundColor,
      borderLeft: messageElement.style.borderLeft,
      element: messageElement,
    });

    messageElement.scrollIntoView({ behavior: "smooth" });

    /* Original table code - commented out but preserved
    // Create container for the parameter form
    const formContainer = document.createElement("div");
    formContainer.className =
      "parameter-form bg-white border border-indigo-200 rounded-lg p-4 mb-4";
    formContainer.id = "parameter-form-container";

    // Create form element
    const form = document.createElement("form");
    form.className = "space-y-4";

    // Create table for parameters
    const table = document.createElement("table");
    table.className = "w-full border-collapse";

    // Add table header (with Vietnamese translation)
    const thead = document.createElement("thead");
    const headerRow = document.createElement("tr");
    const isVietnamese = document.documentElement.lang === "vi";
    headerRow.innerHTML = `
      <th class="text-left py-2 px-3 bg-indigo-100 border-b-2 border-indigo-200">Parameter</th>
      <th class="text-left py-2 px-3 bg-indigo-100 border-b-2 border-indigo-200">Value</th>
    `;
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Add table body
    const tbody = document.createElement("tbody");

    // Add a row for each missing parameter
    const cleanedQuestions = questions.map((q) => {
      // Remove trailing punctuation and question marks
      let cleaned = q.replace(/[?:.,;!]+$/, "").trim();
      // If the question is just "Sheet thickness", "Hole radius", etc., add a label
      if (
        cleaned.toLowerCase().includes("thickness") &&
        !cleaned.includes("=")
      ) {
        cleaned = "Sheet thickness";
      }
      if (cleaned.toLowerCase().includes("radius") && !cleaned.includes("=")) {
        cleaned = "Hole radius";
      }
      if (cleaned.toLowerCase().includes("pattern") && !cleaned.includes("=")) {
        cleaned = "Hole pattern";
      }
      return cleaned;
    });

    cleanedQuestions.forEach((question, index) => {
      const row = document.createElement("tr");
      row.className = index % 2 === 0 ? "bg-gray-50" : "bg-white";

      // Parameter name cell
      const nameCell = document.createElement("td");
      nameCell.className = "py-2 px-3 border-b border-gray-200";
      nameCell.textContent = question;

      // Parameter value cell
      const valueCell = document.createElement("td");
      valueCell.className = "py-2 px-3 border-b border-gray-200";

      // Create input for parameter value
      const input = document.createElement("input");
      input.type = "text";
      input.name = `param_${index}`;
      input.className =
        "w-full border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500";
      input.placeholder = "Enter value";
      input.required = true;

      valueCell.appendChild(input);

      // Add cells to row
      row.appendChild(nameCell);
      row.appendChild(valueCell);

      // Add row to table body
      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    form.appendChild(table);

    // Add submit button
    const submitButton = document.createElement("button");
    submitButton.type = "submit";
    submitButton.className =
      "w-full bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 transition";
    submitButton.textContent = "Submit Parameters";
    form.appendChild(submitButton);

    // Add form to container
    formContainer.appendChild(form);

    // Add form container to code output
    codeOutput.appendChild(formContainer);
    formContainer.scrollIntoView({ behavior: "smooth" });

    // Add event listener for form submission
    form.addEventListener("submit", function (e) {
      e.preventDefault();

      // Collect all parameter values
      const paramValues = [];
      cleanedQuestions.forEach((question, index) => {
        const input = form.querySelector(`input[name="param_${index}"]`);
        // Use the original question text for better context
        paramValues.push(`${question}: ${input.value}`);
      });

      // Join all parameter values into a single message
      const combinedMessage = paramValues.join(", ");

      // Remove the form
      formContainer.remove();

      // Display a message showing the submitted parameters
      const submittedMessage = document.createElement("div");
      submittedMessage.className = "message user";
      submittedMessage.innerHTML = `<strong>Submitted Parameters:</strong> ${combinedMessage}`;
      codeOutput.appendChild(submittedMessage);

      // Show typing indicator
      showTypingIndicator();

      // Send request to API
      fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: combinedMessage,
          is_edit_request: isEditMode,
          session_id: currentSessionId, // Include session_id for conversation continuity
        }),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
          }
          return response.json();
        })
        .then((data) => {
          // Remove typing indicator
          removeTypingIndicator();

          console.log("Server response:", data); // Log the response for debugging

          // Store session_id from response for continuity
          if (data.session_id) {
            updateSessionIndicator(data.session_id);
          }

          // Check if there's a message asking for more parameters
          if (data.message) {
            console.log(
              "Received message from server after parameter submission:",
              data.message
            );

            // Display the message asking for more parameters
            displayMessage(data.message, "bot");

            // If there's an explanation, display it as well
            if (data.explanation) {
              console.log(
                "Received explanation from server after parameter submission:",
                data.explanation
              );
              setTimeout(() => {
                displayMessage(`<i>${data.explanation}</i>`, "bot-explanation");
              }, 500);
            }
          }
          // Display generated code if available
          else if (data.code) {
            displayCode(data.code);
            // Store the latest code
            latestCode = data.code;
          }
          // Display error if present
          else if (data.error) {
            displayError(data.error);
          }
          // Handle unexpected response format
          else {
            console.warn("Unexpected response format:", data);
            displayError(
              "Received an unexpected response format from the server."
            );
          }

          sendBtn.disabled = false;
        })
        .catch((error) => {
          console.error("Error:", error);
          removeTypingIndicator();

          // Provide more detailed error information
          let errorMessage =
            "Sorry, an error occurred while connecting to the server.";
          if (error.message) {
            errorMessage += ` Details: ${error.message}`;
          }

          displayError(errorMessage);
          sendBtn.disabled = false;
        });
    });
    */
  }

  // Add CSS for messages and explanations
  const style = document.createElement("style");
  style.textContent = `
    .bot-explanation {
      font-size: 0.9em;
      color: #666;
      background-color: #f5f5f5;
      border-left: 3px solid #ccc;
      margin-left: 20px;
      padding: 5px 10px;
    }

    #code-output .message {
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 8px;
      max-width: 90%;
    }

    #code-output .message.bot {
      background-color: #e8f4ff !important;
      border-left: 3px solid #4299e1 !important;
      color: #2c5282 !important;
    }

    /* Parameter table styles */
    .parameter-form {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      background-color: #f8faff;
      border: 2px solid #4299e1;
      animation: highlight-form 1s ease-in-out;
    }

    @keyframes highlight-form {
      0% { transform: scale(0.98); opacity: 0.8; }
      50% { transform: scale(1.02); opacity: 1; }
      100% { transform: scale(1); }
    }

    .parameter-form:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.18);
    }

    .parameter-form table {
      border-radius: 4px;
      overflow: hidden;
      width: 100%;
    }

    .parameter-form th {
      background-color: #4299e1;
      color: white;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.85rem;
      letter-spacing: 0.05em;
    }

    .parameter-form input {
      border: 1px solid #cbd5e0;
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
      width: 100%;
      transition: all 0.2s;
    }

    .parameter-form input:focus {
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
      outline: none;
    }

    .parameter-form button {
      font-weight: 600;
      transition: all 0.2s ease;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-top: 1rem;
    }

    .parameter-form button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  `;
  document.head.appendChild(style);

  // Add CSS for PDF processing, notifications, and drag & drop
  const enhancedStyles = document.createElement("style");
  enhancedStyles.textContent = `
    #pdf-processing, #image-processing, #multi-file-processing {
      margin: 20px 0;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.6;
      }
      100% {
        opacity: 1;
      }
    }

    @keyframes fadeOut {
      0% {
        opacity: 1;
        transform: translateY(0);
      }
      80% {
        opacity: 1;
        transform: translateY(0);
      }
      100% {
        opacity: 0;
        transform: translateY(-10px);
      }
    }

    @keyframes slideInFadeOut {
      0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
      15% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
      85% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
      100% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
    }

    .fade-out {
      animation: fadeOut 5s forwards;
    }

    /* Enhanced drag and drop styles */
    .drag-over {
      background-color: #e8f4ff !important;
      border-color: #4299e1 !important;
      transform: scale(1.01);
      transition: all 0.3s ease;
      box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15) !important;
    }

    .drag-over #paste-hint {
      opacity: 1 !important;
      transform: scale(1.05);
    }


    /* Image preview styles */
    .image-preview-container {
      position: relative;
      transition: all 0.2s ease;
    }

    .image-preview-container:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .image-preview-container img {
      transition: transform 0.2s ease;
    }

    .image-preview-container:hover img {
      transform: scale(1.1);
    }

    /* Enhanced input area styling */
    #user-input {
      transition: all 0.3s ease;
    }

    #user-input:focus {
      outline: none;
    }

    /* File display animations */
    #selected-file-info > div {
      animation: slideInFromTop 0.3s ease-out;
    }

    @keyframes slideInFromTop {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Button hover effects */
    button {
      transition: all 0.2s ease;
    }

    button:hover {
      transform: translateY(-1px);
    }

    button:active {
      transform: translateY(0);
    }

    /* Chat form container enhancements */
    .bg-white.rounded-xl {
      transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
    }
  `;
  document.head.appendChild(enhancedStyles);

  // STEP viewer button event listener
  viewStepBtn.addEventListener("click", function () {
    console.log("CLIENT: View STEP button pressed");

    // Prevent multiple clicks
    if (viewStepBtn.disabled) {
      return;
    }

    launchStepViewer();
  });

  // Enhanced function to launch STEP viewer
  function launchStepViewer() {
    console.log("Launching 3D Model Viewer...");

    // Show enhanced loading state
    const originalContent = viewStepBtn.innerHTML;
    viewStepBtn.disabled = true;
    viewStepBtn.className = viewStepBtn.className.replace(
      "bg-green-600",
      "bg-gray-400"
    );
    viewStepBtn.innerHTML =
      '<div class="loading-spinner-small mr-2"></div>Opening 3D Viewer...';

    fetch("/api/launch-step-viewer", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Server responded with status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        if (data.success) {
          console.log("3D Model Viewer launched successfully");
          // Show success state
          viewStepBtn.className = viewStepBtn.className.replace(
            "bg-gray-400",
            "bg-green-500"
          );
          viewStepBtn.innerHTML = '<i class="fas fa-check mr-2"></i>3D Viewer Opened!';

          // Show success notification
          displaySuccessMessage(`3D Model Viewer opened successfully! ${data.message || ''}`);

          setTimeout(() => {
            viewStepBtn.innerHTML = originalContent;
            viewStepBtn.className = viewStepBtn.className.replace(
              "bg-green-500",
              "bg-green-600"
            );
            viewStepBtn.disabled = false;
          }, 2000);
        } else {
          throw new Error(data.message || "Failed to launch 3D Model Viewer");
        }
      })
      .catch((error) => {
        console.error("Error launching 3D Model Viewer:", error);

        // Show error state
        viewStepBtn.className = viewStepBtn.className.replace(
          "bg-gray-400",
          "bg-red-500"
        );
        viewStepBtn.innerHTML =
          '<i class="fas fa-exclamation-triangle mr-2"></i>Error';

        setTimeout(() => {
          viewStepBtn.innerHTML = originalContent;
          viewStepBtn.className = viewStepBtn.className.replace(
            "bg-red-500",
            "bg-green-600"
          );
          viewStepBtn.disabled = false;
        }, 3000);

        displayError(`Error launching 3D Model Viewer: ${error.message}`);
      });
  }

  // Function to display success messages
  function displaySuccessMessage(message) {
    const notification = document.createElement("div");
    notification.className = "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 fade-out";
    notification.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        <span>${message}</span>
      </div>
    `;
    notification.style.animation = "fadeOut 5s forwards";

    // Insert before code output
    codeOutput.parentNode.insertBefore(notification, codeOutput);

    // Remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  // Function to update session indicator
  function updateSessionIndicator(sessionId) {
    console.log(`📋 [SESSION DEBUG] Session updated: ${sessionId || "None"}`);

    if (sessionId) {
      currentSessionId = sessionId;
      sessionIdDisplay.textContent = sessionId;
      sessionIndicator.classList.remove("hidden");

      // Add debug info to the session indicator
      sessionIndicator.title = `Current session: ${sessionId}\nClick to copy session ID`;
      sessionIndicator.style.cursor = "pointer";

      // Add click handler to copy session ID
      sessionIndicator.onclick = function () {
        navigator.clipboard.writeText(sessionId).then(() => {
          console.log(`📋 [SESSION DEBUG] Session ID copied: ${sessionId}`);

          // Show temporary notification
          const notification = document.createElement("div");
          notification.className = "text-sm text-blue-600 mt-1 mb-2 fade-out";
          notification.textContent = "Session ID copied to clipboard!";
          notification.style.animation = "fadeOut 2s forwards";

          sessionIndicator.parentNode.insertBefore(
            notification,
            sessionIndicator.nextSibling
          );

          setTimeout(() => {
            notification.remove();
          }, 2000);
        });
      };

      console.log("✅ [SESSION DEBUG] Session indicator updated:", sessionId);
    } else {
      sessionIndicator.classList.add("hidden");
      sessionIdDisplay.textContent = "";
      currentSessionId = null;
      sessionIndicator.onclick = null;
      console.log("❌ [SESSION DEBUG] Session indicator hidden");
    }
  }
});
