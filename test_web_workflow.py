#!/usr/bin/env python3
"""
Test script to simulate the complete web workflow:
1. Start server
2. Generate a model
3. Test View 3D Model button functionality
"""

import requests
import time
import subprocess
import sys
import os
import json
from pathlib import Path

def test_server_connection(base_url="http://localhost:8124"):
    """Test if server is running."""
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def test_model_generation(base_url="http://localhost:8124"):
    """Test generating a simple model."""
    try:
        data = {
            "message": "rectangular box 20x30x40",
            "is_edit_request": False,
            "session_id": None
        }
        
        print("📤 Sending model generation request...")
        response = requests.post(f"{base_url}/api/chat", json=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Model generated successfully")
            print(f"   Session ID: {result.get('session_id', 'N/A')}")
            print(f"   Generated code: {'Yes' if result.get('generated_code') else 'No'}")
            return True, result
        else:
            print(f"❌ Model generation failed: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception during model generation: {e}")
        return False, None

def test_view_3d_model(base_url="http://localhost:8124"):
    """Test the View 3D Model API endpoint."""
    try:
        print("🎯 Testing View 3D Model endpoint...")
        response = requests.post(f"{base_url}/api/launch-step-viewer", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ View 3D Model successful: {result.get('message', '')}")
                return True
            else:
                print(f"❌ View 3D Model failed: {result.get('message', '')}")
                return False
        else:
            print(f"❌ View 3D Model API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during View 3D Model test: {e}")
        return False

def test_multiple_launches(base_url="http://localhost:8124", count=3):
    """Test launching 3D viewer multiple times."""
    print(f"\n🔄 Testing multiple launches ({count} times)...")
    
    success_count = 0
    for i in range(count):
        print(f"   Launch {i+1}/{count}...")
        if test_view_3d_model(base_url):
            success_count += 1
            time.sleep(2)  # Wait between launches
        else:
            break
    
    print(f"📊 Multiple launch results: {success_count}/{count} successful")
    return success_count == count

def check_step_files():
    """Check if STEP files exist."""
    print("\n📁 Checking for STEP files...")
    
    step_files = []
    
    # Check in cad_outputs_generated
    cad_outputs_dir = Path("outputs/code/cad_outputs_generated")
    if cad_outputs_dir.exists():
        step_files.extend(list(cad_outputs_dir.glob("*.step")))
    
    # Check in organized outputs
    cad_dir = Path("outputs/cad")
    if cad_dir.exists():
        step_files.extend(list(cad_dir.glob("**/*.step")))
    
    if step_files:
        # Sort by modification time
        step_files.sort(key=lambda p: p.stat().st_mtime, reverse=True)
        print(f"✅ Found {len(step_files)} STEP files")
        print(f"   Most recent: {step_files[0]}")
        return True
    else:
        print("❌ No STEP files found")
        return False

def main():
    """Main test function."""
    print("🧪 Web Workflow Test for View 3D Model")
    print("=" * 60)
    
    base_url = "http://localhost:8124"
    
    # Step 1: Check server
    print("1. Checking server connection...")
    if not test_server_connection(base_url):
        print("❌ Server is not running!")
        print("   Please start the server with: python run.py")
        return False
    print("✅ Server is running")
    
    # Step 2: Check existing STEP files
    has_existing_files = check_step_files()
    
    # Step 3: Generate model if needed
    if not has_existing_files:
        print("\n2. Generating a test model...")
        success, result = test_model_generation(base_url)
        if not success:
            print("❌ Cannot proceed without a model")
            return False
    else:
        print("\n2. Using existing STEP files...")
    
    # Step 4: Test View 3D Model once
    print("\n3. Testing View 3D Model (single launch)...")
    if not test_view_3d_model(base_url):
        print("❌ Single launch failed")
        return False
    
    # Step 5: Test multiple launches
    print("\n4. Testing View 3D Model (multiple launches)...")
    if not test_multiple_launches(base_url, 3):
        print("❌ Multiple launches failed")
        return False
    
    # Step 6: Final verification
    print("\n5. Final verification...")
    time.sleep(2)
    if test_view_3d_model(base_url):
        print("✅ Final test successful")
    else:
        print("⚠️ Final test failed")
    
    print("\n" + "=" * 60)
    print("🎉 Web Workflow Test Complete!")
    print("\nWhat to check manually:")
    print("1. 3D Model Viewer windows should have opened")
    print("2. Each window should show the 3D model")
    print("3. Windows should be visible and interactive")
    print("4. You can close and reopen viewers using the web button")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ All tests passed!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
