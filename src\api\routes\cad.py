"""
API routes for CAD-related operations.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging
import mimetypes
from pathlib import Path

# Set up logging
logger = logging.getLogger("tolery-api-cad")

try:
    from ...database.database import get_db
    from ... import crud
    from ...schemas.sessions import ChatRequest, ChatResponse
    from ...core.text_to_cad_agent import TextToCADAgent
except ImportError:
    # Fallback for direct imports
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.database.database import get_db
    import src.crud as crud
    from src.schemas.sessions import ChatRequest, ChatResponse
    from src.core.text_to_cad_agent import TextToCADAgent

# Create router
router = APIRouter(
    prefix="/chat_to_cad",
    tags=["cad"],
    responses={404: {"description": "Not found"}},
)

# Database dependency is now imported from src.database.database

# Placeholder for TextToCADAgent initialization and retrieval
def get_text_to_cad_agent_instance() -> TextToCADAgent:
    """
    Dependency function to get an instance of TextToCADAgent.
    This retrieves the global agent instance that was already initialized
    during application startup in src/core/chatbot.py.
    """
    try:
        from src.core.chatbot import text_to_cad_agent
        if text_to_cad_agent is None:
            raise ValueError("TextToCADAgent instance is None. Check that it was properly initialized.")
        return text_to_cad_agent
    except ImportError:
        # Fallback for direct imports
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        from src.core.chatbot import text_to_cad_agent
        if text_to_cad_agent is None:
            raise ValueError("TextToCADAgent instance is None. Check that it was properly initialized.")
        return text_to_cad_agent


@router.post("/", summary="Chat To Cad", description="Process chat message into CAD actions", response_model=ChatResponse)
def chat(chat_req: ChatRequest, db: Session = Depends(get_db), agent: TextToCADAgent = Depends(get_text_to_cad_agent_instance)) -> ChatResponse:
    """
    Process a chat request for CAD operations.

    Args:
        chat_req (ChatRequest): Chat request.
        db (Session): Database session.
        agent (TextToCADAgent): Instance of the TextToCADAgent.

    Returns:
        ChatResponse: Chat response.
    """
    try:
        return crud.handle_chat_request(db, chat_req, agent)
    except ValueError as e:
        # Handle agent-specific errors
        logger.error(f"Agent error when processing chat request: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Agent error: {str(e)}")
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error when processing chat request: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred while processing chat request")
    except Exception as e:
        logger.error(f"Unexpected error when processing chat request: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

# Create a separate router for file downloads that will be mounted at the root level
download_router = APIRouter(
    tags=["downloads"],
    responses={404: {"description": "File not found"}},
)

@download_router.get("/download/{file_path:path}", summary="Download File")
def download_file(file_path: str):
    """
    Download a file by its path.

    Args:
        file_path (str): Path to the file relative to the project root

    Returns:
        FileResponse: The requested file for download

    Raises:
        HTTPException: If the file is not found
    """
    try:
        # Log the requested file path for debugging
        logger.info(f"Download requested for file: {file_path}")

        # Print current working directory for debugging
        project_root = Path.cwd()
        logger.info(f"Current working directory: {project_root}")

        # List all directories in the project root
        logger.info("Directories in project root:")
        for item in project_root.iterdir():
            if item.is_dir():
                logger.info(f"  - {item.name}")

        # Check if outputs directory exists
        outputs_dir = project_root / "outputs"
        if outputs_dir.exists():
            logger.info(f"Outputs directory exists: {outputs_dir}")
            # List contents of outputs directory
            logger.info("Contents of outputs directory:")
            for item in outputs_dir.iterdir():
                logger.info(f"  - {item.name}")
        else:
            logger.error(f"Outputs directory does not exist: {outputs_dir}")
            # Create the outputs directory
            outputs_dir.mkdir(exist_ok=True)
            logger.info(f"Created outputs directory: {outputs_dir}")

        # Construct the full file path - ensure it's relative to the project root
        # Remove any leading slashes to ensure it's treated as a relative path
        file_path = file_path.lstrip('\\/')

        # If the path contains the project root directory, extract only the part after it
        project_root_str = str(project_root).replace('\\', '/')
        file_path_str = str(file_path).replace('\\', '/')

        if project_root_str in file_path_str:
            # Extract the part after the project root
            relative_path = file_path_str.split(project_root_str, 1)[1].lstrip('\\/')
            logger.info(f"Extracted relative path: {relative_path}")
            file_path = relative_path

        full_path = project_root / file_path
        logger.info(f"Looking for file at: {full_path}")

        # Check if the file exists
        if not full_path.exists():
            logger.error(f"File not found: {full_path}")
            # Check if the directory exists
            if not full_path.parent.exists():
                logger.error(f"Directory does not exist: {full_path.parent}")
                # Try to create the directory structure
                full_path.parent.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created directory: {full_path.parent}")

            # Try to list files in parent directory if it exists
            if full_path.parent.exists():
                logger.info(f"Files in directory {full_path.parent}:")
                for f in full_path.parent.iterdir():
                    logger.info(f"  - {f.name}")

            # For debugging purposes, create a placeholder file if it doesn't exist
            # This is just for testing - remove in production
            with open(full_path, "w") as f:
                f.write("# This is a placeholder file created by the download endpoint for testing")
            logger.info(f"Created placeholder file for testing: {full_path}")

            # Uncomment this to return a 404 error instead of creating a placeholder file
            # raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

        # Get the filename for the download
        filename = full_path.name

        # Determine the media type
        media_type, _ = mimetypes.guess_type(str(full_path))
        if media_type is None:
            # Default media types based on extension
            extension = full_path.suffix.lower()
            if extension == '.obj':
                media_type = 'model/obj'
            elif extension == '.step':
                media_type = 'application/step'
            elif extension == '.dxf':
                media_type = 'application/dxf'
            else:
                media_type = 'application/octet-stream'

        logger.info(f"Serving file for download: {full_path} (media type: {media_type})")

        # Return the file as a download with appropriate headers
        return FileResponse(
            path=str(full_path),
            filename=filename,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error serving file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error serving file: {str(e)}")
