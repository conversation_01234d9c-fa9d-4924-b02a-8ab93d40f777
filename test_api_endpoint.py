#!/usr/bin/env python3
"""
Test the /api/launch-step-viewer endpoint directly.
"""

import requests
import time
import json

def test_api_endpoint(base_url="http://localhost:8124"):
    """Test the API endpoint directly."""
    print("🧪 Testing /api/launch-step-viewer endpoint")
    print("=" * 50)
    
    endpoint = f"{base_url}/api/launch-step-viewer"
    
    try:
        print(f"📤 Sending POST request to: {endpoint}")
        
        # Send request
        response = requests.post(endpoint, 
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 Response data:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print("✅ API call successful!")
                print(f"   Message: {result.get('message', 'N/A')}")
                return True
            else:
                print("❌ API call failed!")
                print(f"   Error: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
        print("   Start server with: python run.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_multiple_calls(base_url="http://localhost:8124", count=3):
    """Test multiple API calls."""
    print(f"\n🔄 Testing multiple API calls ({count} times)")
    print("=" * 50)
    
    success_count = 0
    for i in range(count):
        print(f"\n📞 Call {i+1}/{count}:")
        if test_api_endpoint(base_url):
            success_count += 1
            print(f"   ✅ Call {i+1} successful")
        else:
            print(f"   ❌ Call {i+1} failed")
        
        if i < count - 1:  # Don't wait after last call
            print("   ⏳ Waiting 3 seconds before next call...")
            time.sleep(3)
    
    print(f"\n📊 Results: {success_count}/{count} calls successful")
    return success_count == count

def check_server_status(base_url="http://localhost:8124"):
    """Check if server is running."""
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    """Main test function."""
    print("🚀 API Endpoint Test for View 3D Model")
    print("=" * 60)
    
    base_url = "http://localhost:8124"
    
    # Check server
    print("1. Checking server status...")
    if not check_server_status(base_url):
        print("❌ Server is not running!")
        print("   Please start the server with: python run.py")
        return False
    print("✅ Server is running")
    
    # Test single call
    print("\n2. Testing single API call...")
    if not test_api_endpoint(base_url):
        print("❌ Single API call failed")
        return False
    
    # Test multiple calls
    print("\n3. Testing multiple API calls...")
    if not test_multiple_calls(base_url, 3):
        print("❌ Multiple API calls failed")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 API Test Complete!")
    print("\nWhat to check manually:")
    print("1. 3D Model Viewer windows should have opened")
    print("2. Each API call should open a new window")
    print("3. Windows should be visible and positioned correctly")
    print("4. Check taskbar for multiple viewer windows")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ All API tests passed!")
        else:
            print("\n❌ Some API tests failed!")
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
