#!/usr/bin/env python3
"""
Test script to debug 3D Model Viewer launch issues.
This script mimics the exact behavior of the backend API.
"""

import subprocess
import sys
import os
import glob
import time
import platform
from pathlib import Path

def find_most_recent_step_file():
    """Find the most recent STEP file using the same logic as the backend."""
    project_root = Path.cwd()
    step_files = []

    # Check in cad_outputs_generated directory first
    cad_outputs_dir = project_root / "outputs" / "code" / "cad_outputs_generated"
    if cad_outputs_dir.exists():
        for step_file in glob.glob(str(cad_outputs_dir / "*.step")):
            step_files.append(step_file)

    # Check in organized CAD outputs
    cad_dir = project_root / "outputs" / "cad"
    if cad_dir.exists():
        for step_file in glob.glob(str(cad_dir / "**/*.step"), recursive=True):
            step_files.append(step_file)

    # Check in project directory
    for step_file in glob.glob(str(project_root / "**/*.step"), recursive=True):
        step_files.append(step_file)

    if not step_files:
        return None

    # Sort by modification time (newest first)
    step_files.sort(key=lambda p: os.path.getmtime(p), reverse=True)
    return step_files[0]

def find_gui_script():
    """Find the GUI script using the same logic as the backend."""
    gui_script_path = os.path.abspath("gui_step.py")

    if not os.path.exists(gui_script_path):
        gui_script_path = os.path.abspath(os.path.join("Tolery", "gui_step.py"))

    return gui_script_path if os.path.exists(gui_script_path) else None

def test_gui_launch():
    """Test launching the GUI with detailed debugging."""
    print("🔍 Testing 3D Model Viewer Launch")
    print("=" * 50)

    # Step 1: Find STEP file
    print("1. Finding most recent STEP file...")
    step_file = find_most_recent_step_file()
    if not step_file:
        print("❌ No STEP files found!")
        return False

    print(f"✅ Found STEP file: {step_file}")
    print(f"   File exists: {os.path.exists(step_file)}")
    print(f"   File size: {os.path.getsize(step_file)} bytes")

    # Step 2: Find GUI script
    print("\n2. Finding GUI script...")
    gui_script = find_gui_script()
    if not gui_script:
        print("❌ GUI script not found!")
        return False

    print(f"✅ Found GUI script: {gui_script}")
    print(f"   Script exists: {os.path.exists(gui_script)}")

    # Step 3: Test Python executable
    print(f"\n3. Python executable: {sys.executable}")
    print(f"   Python version: {sys.version}")
    print(f"   Platform: {platform.system()}")

    # Step 4: Test command construction
    command = [sys.executable, gui_script, str(step_file)]
    print(f"\n4. Command to execute:")
    print(f"   {' '.join(command)}")

    # Step 5: Launch process
    print("\n5. Launching process...")
    try:
        env = os.environ.copy()

        if platform.system() == "Windows":
            print("   Using Windows-specific launch (DETACHED_PROCESS)")
            process = subprocess.Popen(command,
                                     cwd=os.getcwd(),
                                     env=env,
                                     creationflags=subprocess.DETACHED_PROCESS)
        else:
            print("   Using standard launch")
            process = subprocess.Popen(command,
                                     cwd=os.getcwd(),
                                     env=env)

        print(f"✅ Process launched with PID: {process.pid}")

        # Step 6: Check if process is running
        print("\n6. Checking process status...")
        time.sleep(1.0)  # Give more time for GUI to start

        poll_result = process.poll()
        if poll_result is not None:
            print(f"❌ Process terminated with exit code: {poll_result}")
            stdout, stderr = process.communicate()
            print(f"STDOUT: {stdout.decode() if stdout else 'None'}")
            print(f"STDERR: {stderr.decode() if stderr else 'None'}")
            return False
        else:
            print("✅ Process is still running")
            print("🪟 GUI should be visible now!")

            # Wait a bit more and check again
            time.sleep(2.0)
            poll_result = process.poll()
            if poll_result is not None:
                print(f"⚠️ Process terminated after 3 seconds with exit code: {poll_result}")
                return False
            else:
                print("✅ Process is stable and running")
                return True

    except Exception as e:
        print(f"❌ Exception during launch: {e}")
        return False

def test_direct_gui_launch():
    """Test launching GUI directly without subprocess."""
    print("\n" + "=" * 50)
    print("🔍 Testing Direct GUI Launch")
    print("=" * 50)

    step_file = find_most_recent_step_file()
    if not step_file:
        print("❌ No STEP files found!")
        return False

    print(f"Attempting to import and run GUI directly...")
    print(f"STEP file: {step_file}")

    try:
        # Change to Tolery directory
        original_cwd = os.getcwd()
        os.chdir("Tolery")

        # Add to Python path
        sys.path.insert(0, os.getcwd())

        # Import and run
        import gui_step
        print("✅ GUI module imported successfully")

        # Restore directory
        os.chdir(original_cwd)

        return True

    except Exception as e:
        print(f"❌ Exception during direct import: {e}")
        os.chdir(original_cwd)
        return False

if __name__ == "__main__":
    print("🚀 3D Model Viewer Launch Test")
    print("This script tests the same launch logic as the backend API")
    print()

    # Test subprocess launch
    success1 = test_gui_launch()

    # Test direct launch
    success2 = test_direct_gui_launch()

    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Subprocess launch: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"   Direct launch: {'✅ SUCCESS' if success2 else '❌ FAILED'}")

    if success1:
        print("\n✅ The GUI should be working correctly!")
        print("   If you don't see the window, check:")
        print("   - Window might be behind other windows")
        print("   - Check taskbar for the application")
        print("   - Try Alt+Tab to cycle through windows")
    else:
        print("\n❌ There's an issue with the GUI launch")
        print("   Check the error messages above for details")
