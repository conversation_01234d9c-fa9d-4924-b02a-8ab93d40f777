#!/usr/bin/env python3
"""
Debug script to find why GUI is not visible.
"""

import subprocess
import sys
import os
import time
import psutil
from pathlib import Path

def check_running_processes():
    """Check if any Python processes are running gui_step.py"""
    print("🔍 Checking for running GUI processes...")
    
    gui_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('gui_step.py' in str(arg) for arg in cmdline):
                    gui_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': ' '.join(cmdline) if cmdline else 'N/A'
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if gui_processes:
        print(f"✅ Found {len(gui_processes)} GUI processes:")
        for proc in gui_processes:
            print(f"   PID {proc['pid']}: {proc['cmdline']}")
        return True
    else:
        print("❌ No GUI processes found")
        return False

def test_gui_with_output_capture():
    """Test GUI launch with output capture to see errors."""
    print("\n🔍 Testing GUI with output capture...")
    
    step_file = "outputs/code/cad_outputs_generated/Rectangular_box.step"
    if not os.path.exists(step_file):
        print(f"❌ STEP file not found: {step_file}")
        return False
    
    gui_script = "Tolery/gui_step.py"
    if not os.path.exists(gui_script):
        print(f"❌ GUI script not found: {gui_script}")
        return False
    
    command = [sys.executable, gui_script, step_file]
    print(f"Command: {' '.join(command)}")
    
    try:
        # Launch with output capture
        process = subprocess.Popen(command,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        print(f"✅ Process launched with PID: {process.pid}")
        
        # Wait a bit and check output
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Process is still running")
            
            # Try to get some output
            try:
                stdout, stderr = process.communicate(timeout=2)
                print(f"STDOUT:\n{stdout}")
                if stderr:
                    print(f"STDERR:\n{stderr}")
            except subprocess.TimeoutExpired:
                print("⏱️ Process is running (timeout on communicate)")
                process.kill()
                stdout, stderr = process.communicate()
                print(f"STDOUT:\n{stdout}")
                if stderr:
                    print(f"STDERR:\n{stderr}")
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Process terminated with code: {process.returncode}")
            print(f"STDOUT:\n{stdout}")
            if stderr:
                print(f"STDERR:\n{stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_simple_pyqt():
    """Test if PyQt5 works at all."""
    print("\n🔍 Testing basic PyQt5 functionality...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        
        app = QApplication(sys.argv)
        print("✅ QApplication created successfully")
        
        # Try to show a simple message box
        msg = QMessageBox()
        msg.setWindowTitle("Test")
        msg.setText("PyQt5 is working!")
        msg.setStandardButtons(QMessageBox.Ok)
        
        # Show for 2 seconds then close
        msg.show()
        print("✅ MessageBox shown")
        
        # Process events briefly
        app.processEvents()
        time.sleep(2)
        
        msg.close()
        app.quit()
        print("✅ PyQt5 basic test successful")
        return True
        
    except Exception as e:
        print(f"❌ PyQt5 test failed: {e}")
        return False

def test_display_environment():
    """Check display environment variables."""
    print("\n🔍 Checking display environment...")
    
    # Check Windows display
    if os.name == 'nt':
        print("Platform: Windows")
        
        # Check if we're in a remote session
        if os.environ.get('SESSIONNAME') == 'RDP-Tcp#0':
            print("⚠️ Running in RDP session - GUI might not display properly")
        
        # Check display scaling
        try:
            import ctypes
            user32 = ctypes.windll.user32
            screensize = user32.GetSystemMetrics(0), user32.GetSystemMetrics(1)
            print(f"Screen resolution: {screensize}")
        except:
            print("Could not get screen resolution")
    
    # Check environment variables
    relevant_vars = ['DISPLAY', 'SESSIONNAME', 'TERM', 'SSH_CLIENT']
    for var in relevant_vars:
        value = os.environ.get(var)
        if value:
            print(f"{var}: {value}")

def kill_existing_gui_processes():
    """Kill any existing GUI processes."""
    print("\n🔧 Killing existing GUI processes...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('gui_step.py' in str(arg) for arg in cmdline):
                    print(f"Killing PID {proc.info['pid']}")
                    proc.kill()
                    killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"Killed {killed_count} processes")
    time.sleep(1)

def main():
    """Main debug function."""
    print("🐛 GUI Debug Script")
    print("=" * 50)
    
    # Step 1: Check environment
    test_display_environment()
    
    # Step 2: Check for existing processes
    check_running_processes()
    
    # Step 3: Kill existing processes
    kill_existing_gui_processes()
    
    # Step 4: Test basic PyQt5
    if not test_simple_pyqt():
        print("❌ Basic PyQt5 test failed - GUI won't work")
        return False
    
    # Step 5: Test GUI with output capture
    if not test_gui_with_output_capture():
        print("❌ GUI launch test failed")
        return False
    
    # Step 6: Check processes again
    time.sleep(2)
    check_running_processes()
    
    print("\n" + "=" * 50)
    print("🎯 Debug Summary:")
    print("1. If PyQt5 MessageBox appeared, PyQt5 is working")
    print("2. If GUI process is running, the launch is successful")
    print("3. If you still don't see the 3D viewer:")
    print("   - Check taskbar for minimized windows")
    print("   - Try Alt+Tab to cycle through windows")
    print("   - The window might be off-screen")
    print("   - Try moving mouse to screen edges")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Debug interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
